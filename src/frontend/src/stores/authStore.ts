// authStore.js
import { LANGFLOW_ACCESS_TOKEN } from "@/constants/constants";
import { AuthStoreType } from "@/types/zustand/auth";
import { Cookies } from "react-cookie";
import { create } from "zustand";

const cookies = new Cookies();

// Helper function to get access token from Firebase Auth or legacy cookies
const getInitialAccessToken = () => {
  // First try Firebase Auth token from localStorage
  const firebaseToken = localStorage.getItem('accessToken');
  if (firebaseToken) {
    return firebaseToken;
  }
  // Fallback to legacy Langflow token from cookies
  return cookies.get(LANGFLOW_ACCESS_TOKEN) ?? null;
};

// Helper function to check if user is authenticated
const getInitialAuthState = () => {
  const firebaseUser = localStorage.getItem('user');
  const firebaseToken = localStorage.getItem('accessToken');
  const legacyToken = cookies.get(LANGFLOW_ACCESS_TOKEN);

  return !!(firebaseUser && firebaseToken) || !!legacyToken;
};

export const useAuthStore = create<AuthStoreType>((set, get) => ({
  isAdmin: false,
  isAuthenticated: getInitialAuthState(),
  accessToken: getInitialAccessToken(),
  userData: null,
  autoLogin: null,
  apiKey: cookies.get("apikey_tkn_lflw"),
  authenticationErrorCount: 0,

  setIsAdmin: (isAdmin) => set({ isAdmin }),
  setIsAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
  setAccessToken: (accessToken) => set({ accessToken }),
  setUserData: (userData) => set({ userData }),
  setAutoLogin: (autoLogin) => set({ autoLogin }),
  setApiKey: (apiKey) => set({ apiKey }),
  setAuthenticationErrorCount: (authenticationErrorCount) =>
    set({ authenticationErrorCount }),

  logout: async () => {
    // Clear all authentication state
    get().setIsAuthenticated(false);
    get().setIsAdmin(false);

    // Clear cookies (legacy Langflow auth)
    const cookies = new Cookies();
    cookies.remove(LANGFLOW_ACCESS_TOKEN, { path: "/" });
    cookies.remove("LANGFLOW_REFRESH_TOKEN", { path: "/" });
    cookies.remove("LANGFLOW_AUTO_LOGIN_OPTION", { path: "/" });
    cookies.remove("apikey_tkn_lflw", { path: "/" });

    // Clear localStorage (both legacy and Firebase auth)
    localStorage.removeItem(LANGFLOW_ACCESS_TOKEN);
    localStorage.removeItem("LANGFLOW_REFRESH_TOKEN");
    localStorage.removeItem("accessToken"); // Firebase Auth token
    localStorage.removeItem("refreshToken"); // Firebase Auth refresh token
    localStorage.removeItem("user"); // Firebase Auth user data

    set({
      isAdmin: false,
      userData: null,
      accessToken: null,
      isAuthenticated: false,
      autoLogin: false,
      apiKey: null,
    });

    // Redirect to login page
    window.location.href = "/login";
  },
}));


