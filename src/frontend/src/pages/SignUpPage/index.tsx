import LangflowLogo from "@/assets/LangflowLogo.svg?react";
import InputComponent from "@/components/core/parameterRenderComponent/components/inputComponent";
import { useAddUser } from "@/controllers/API/queries/auth";
import { CustomLink } from "@/customization/components/custom-link";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import { track } from "@/customization/utils/analytics";
import * as Form from "@radix-ui/react-form";
import { FormEvent, useContext, useEffect, useState } from "react";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { SIGNUP_ERROR_ALERT } from "../../constants/alerts_constants";
import {
  CONTROL_INPUT_STATE,
  SIGN_UP_SUCCESS,
} from "../../constants/constants";
import { AuthContext } from "../../contexts/auth/AuthContext";
import { FirebaseAuthIntegration } from "../../services/firebase/authIntegration";
import { useAlertStore } from "../../stores/alertStore";
import { useAuthStore } from "../../stores/authStore";
import { Cookies } from "react-cookie";
import { LANGFLOW_ACCESS_TOKEN, LANGFLOW_REFRESH_TOKEN } from "../../constants/constants";
import {
  UserInputType,
  inputHandlerEventType,
  signUpInputStateType,
} from "../../types/components";

export default function SignUp(): JSX.Element {
  const [inputState, setInputState] =
    useState<signUpInputStateType>(CONTROL_INPUT_STATE);
  const [isFirebaseAuth, setIsFirebaseAuth] = useState<boolean>(false);
  const [isCheckingFirebase, setIsCheckingFirebase] = useState<boolean>(true);
  const [firebaseAuthIntegration] = useState(() => new FirebaseAuthIntegration());

  const [isDisabled, setDisableBtn] = useState<boolean>(true);

  const { password, cnfPassword, username } = inputState;
  const { login, setUserData } = useContext(AuthContext);
  const setSuccessData = useAlertStore((state) => state.setSuccessData);
  const setErrorData = useAlertStore((state) => state.setErrorData);
  const navigate = useCustomNavigate();

  // Auth store methods for route guards
  const setIsAuthenticated = useAuthStore((state) => state.setIsAuthenticated);
  const setAccessToken = useAuthStore((state) => state.setAccessToken);
  const setAuthStoreUserData = useAuthStore((state) => state.setUserData);

  // Cookies for token storage
  const cookies = new Cookies();

  const { mutate: mutateAddUser } = useAddUser();

  // Check Firebase Auth status on component mount
  useEffect(() => {
    const checkFirebaseAuth = async () => {
      try {
        const status = await firebaseAuthIntegration.getAuthStatus();
        setIsFirebaseAuth(status.enabled && status.available);
      } catch (error) {
        console.error('Failed to check Firebase Auth status:', error);
        setIsFirebaseAuth(false);
      } finally {
        setIsCheckingFirebase(false);
      }
    };

    checkFirebaseAuth();
  }, [firebaseAuthIntegration]);

  function handleInput({
    target: { name, value },
  }: inputHandlerEventType): void {
    setInputState((prev) => ({ ...prev, [name]: value }));
  }

  useEffect(() => {
    if (password !== cnfPassword) return setDisableBtn(true);
    if (password === "" || cnfPassword === "") return setDisableBtn(true);
    if (username === "") return setDisableBtn(true);
    setDisableBtn(false);
  }, [password, cnfPassword, username, handleInput]);

  function handleSignup(): void {
    if (isFirebaseAuth) {
      // Use Firebase Auth flow
      signUpWithFirebase();
    } else {
      // Use traditional username/password flow
      const { username, password } = inputState;
      const newUser: UserInputType = {
        username: username.trim(),
        password: password.trim(),
      };

      mutateAddUser(newUser, {
        onSuccess: (user) => {
          track("User Signed Up", user);
          setSuccessData({
            title: SIGN_UP_SUCCESS,
          });
          navigate("/login");
        },
        onError: (error) => {
          const {
            response: {
              data: { detail },
            },
          } = error;
          setErrorData({
            title: SIGNUP_ERROR_ALERT,
            list: [detail],
          });
        },
      });
    }
  }

  async function signUpWithFirebase(): Promise<void> {
    try {
      // For Firebase Auth, treat username as email
      const email = username.trim();
      const pass = password.trim();

      console.log('🔍 Attempting Firebase signup with email:', email);
      const tokens = await firebaseAuthIntegration.signUpWithFirebase(email, pass);

      // Use the tokens to log in to Langflow
      login(tokens.access_token, "login", tokens.refresh_token);

      // Set user data from the response
      if (tokens.user) {
        setUserData(tokens.user);
      }

      // Update auth store for route guards
      setIsAuthenticated(true);
      setAccessToken(tokens.access_token);
      if (tokens.user) {
        setAuthStoreUserData(tokens.user);
      }

      // Set cookies for API interceptors
      cookies.set(LANGFLOW_ACCESS_TOKEN, tokens.access_token, { path: "/" });
      if (tokens.refresh_token) {
        cookies.set(LANGFLOW_REFRESH_TOKEN, tokens.refresh_token, { path: "/" });
      }

      track("User Signed Up", { email });
      setSuccessData({
        title: "Account created successfully!",
      });

      console.log('✅ Firebase signup successful, navigating to home...');
      // Navigate to home page since user is now logged in
      navigate("/");
    } catch (error: any) {
      console.error('Firebase sign-up failed:', error);
      setErrorData({
        title: SIGNUP_ERROR_ALERT,
        list: [error.message || "Firebase account creation failed"],
      });
    }
  }

  return (
    <Form.Root
      onSubmit={(event: FormEvent<HTMLFormElement>) => {
        if (password === "") {
          event.preventDefault();
          return;
        }

        const data = Object.fromEntries(new FormData(event.currentTarget));
        event.preventDefault();
      }}
      className="h-screen w-full"
    >
      <div className="flex h-full w-full flex-col items-center justify-center bg-muted">
        <div className="flex w-72 flex-col items-center justify-center gap-2">
          <LangflowLogo
            title="Langflow logo"
            className="mb-4 h-10 w-10 scale-[1.5]"
          />
          <span className="mb-6 text-2xl font-semibold text-primary">
            Sign up for Langflow
          </span>
          {isCheckingFirebase && (
            <div className="mb-4 text-sm text-muted-foreground">
              Checking authentication method...
            </div>
          )}
          {!isCheckingFirebase && (
            <>
              <div className="mb-3 w-full">
                <Form.Field name="username">
                  <Form.Label className="data-[invalid]:label-invalid">
                    {isFirebaseAuth ? "Email" : "Username"} <span className="font-medium text-destructive">*</span>
                  </Form.Label>

                  <Form.Control asChild>
                    <Input
                      type={isFirebaseAuth ? "email" : "username"}
                      onChange={({ target: { value } }) => {
                        handleInput({ target: { name: "username", value } });
                      }}
                      value={username}
                      className="w-full"
                      required
                      placeholder={isFirebaseAuth ? "Email" : "Username"}
                    />
                  </Form.Control>

                  <Form.Message match="valueMissing" className="field-invalid">
                    Please enter your {isFirebaseAuth ? "email" : "username"}
                  </Form.Message>
                </Form.Field>
              </div>
          <div className="mb-3 w-full">
            <Form.Field name="password" serverInvalid={password != cnfPassword}>
              <Form.Label className="data-[invalid]:label-invalid">
                Password <span className="font-medium text-destructive">*</span>
              </Form.Label>
              <InputComponent
                onChange={(value) => {
                  handleInput({ target: { name: "password", value } });
                }}
                value={password}
                isForm
                password={true}
                required
                placeholder="Password"
                className="w-full"
              />

              <Form.Message className="field-invalid" match="valueMissing">
                Please enter a password
              </Form.Message>

              {password != cnfPassword && (
                <Form.Message className="field-invalid">
                  Passwords do not match
                </Form.Message>
              )}
            </Form.Field>
          </div>
          <div className="w-full">
            <Form.Field
              name="confirmpassword"
              serverInvalid={password != cnfPassword}
            >
              <Form.Label className="data-[invalid]:label-invalid">
                Confirm your password{" "}
                <span className="font-medium text-destructive">*</span>
              </Form.Label>

              <InputComponent
                onChange={(value) => {
                  handleInput({ target: { name: "cnfPassword", value } });
                }}
                value={cnfPassword}
                isForm
                password={true}
                required
                placeholder="Confirm your password"
                className="w-full"
              />

              <Form.Message className="field-invalid" match="valueMissing">
                Please confirm your password
              </Form.Message>
            </Form.Field>
          </div>
              <div className="w-full">
                <Form.Submit asChild>
                  <Button
                    disabled={isDisabled || isCheckingFirebase}
                    type="submit"
                    className="mr-3 mt-6 w-full"
                    onClick={() => {
                      handleSignup();
                    }}
                  >
                    {isCheckingFirebase ? "Loading..." : "Sign up"}
                  </Button>
                </Form.Submit>
              </div>
              <div className="w-full">
                <CustomLink to="/login">
                  <Button className="w-full" variant="outline">
                    Already have an account?&nbsp;<b>Sign in</b>
                  </Button>
                </CustomLink>
              </div>
              {isFirebaseAuth && (
                <div className="mt-4 text-xs text-muted-foreground text-center">
                  Using Firebase Authentication
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Form.Root>
  );
}
