import LangflowLogo from "@/assets/LangflowLogo.svg?react";
import { useLoginUser } from "@/controllers/API/queries/auth";
import { CustomLink } from "@/customization/components/custom-link";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import * as Form from "@radix-ui/react-form";
import { useContext, useEffect, useState } from "react";
import { Cookies } from "react-cookie";
import InputComponent from "../../components/core/parameterRenderComponent/components/inputComponent";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { SIGNIN_ERROR_ALERT } from "../../constants/alerts_constants";
import { CONTROL_LOGIN_STATE, LANGFLOW_ACCESS_TOKEN, LANGFLOW_REFRESH_TOKEN } from "../../constants/constants";
import { AuthContext } from "../../contexts/auth/AuthContext";
import { FirebaseAuthIntegration } from "../../services/firebase/authIntegration";
import { useAlertStore } from "../../stores/alertStore";
import { useAuthStore } from "../../stores/authStore";
import { LoginType } from "../../types/api";
import {
  inputHandlerEventType,
  loginInputStateType,
} from "../../types/components";

export default function LoginPage(): JSX.Element {
  const [inputState, setInputState] =
    useState<loginInputStateType>(CONTROL_LOGIN_STATE);
  const [isFirebaseAuth, setIsFirebaseAuth] = useState<boolean>(false);
  const [isCheckingFirebase, setIsCheckingFirebase] = useState<boolean>(true);
  const [firebaseAuthIntegration] = useState(() => new FirebaseAuthIntegration());

  const { password, username } = inputState;
  const { login, setUserData } = useContext(AuthContext);
  const setErrorData = useAlertStore((state) => state.setErrorData);
  const navigate = useCustomNavigate();

  // Auth store methods for route guards
  const setIsAuthenticated = useAuthStore((state) => state.setIsAuthenticated);
  const setAccessToken = useAuthStore((state) => state.setAccessToken);
  const setAuthStoreUserData = useAuthStore((state) => state.setUserData);

  // Cookies for token storage
  const cookies = new Cookies();

  // Check Firebase Auth status on component mount
  useEffect(() => {
    const checkFirebaseAuth = async () => {
      try {
        console.log('🔄 LoginPage: Starting Firebase auth check...');
        const status = await firebaseAuthIntegration.getAuthStatus();
        console.log('📋 LoginPage: Auth status received:', status);
        const isFirebase = status.enabled && status.available;
        console.log('🔥 LoginPage: Setting isFirebaseAuth to:', isFirebase);
        setIsFirebaseAuth(isFirebase);
      } catch (error) {
        console.error('❌ LoginPage: Failed to check Firebase Auth status:', error);
        setIsFirebaseAuth(false);
      } finally {
        console.log('✅ LoginPage: Auth check complete');
        setIsCheckingFirebase(false);
      }
    };

    checkFirebaseAuth();
  }, [firebaseAuthIntegration]);

  function handleInput({
    target: { name, value },
  }: inputHandlerEventType): void {
    setInputState((prev) => ({ ...prev, [name]: value }));
  }

  const { mutate } = useLoginUser();

  function signIn() {
    if (isFirebaseAuth) {
      // Use Firebase Auth flow
      signInWithFirebase();
    } else {
      // Use traditional username/password flow
      const user: LoginType = {
        username: username.trim(),
        password: password.trim(),
      };

      mutate(user, {
        onSuccess: (data) => {
          login(data.access_token, "login", data.refresh_token);
        },
        onError: (error) => {
          setErrorData({
            title: SIGNIN_ERROR_ALERT,
            list: [error["response"]["data"]["detail"]],
          });
        },
      });
    }
  }

  async function signInWithFirebase() {
    try {
      // For Firebase Auth, treat username as email
      const email = username.trim();
      const pass = password.trim();

      console.log('🔍 Attempting Firebase login with email:', email);
      const tokens = await firebaseAuthIntegration.signInWithFirebase(email, pass);

      // Use the tokens to log in to Langflow
      login(tokens.access_token, "login", tokens.refresh_token);

      // Set user data from the response
      if (tokens.user) {
        setUserData(tokens.user);
      }

      // Update auth store for route guards
      setIsAuthenticated(true);
      setAccessToken(tokens.access_token);
      if (tokens.user) {
        setAuthStoreUserData(tokens.user);
      }

      // Set cookies for API interceptors
      cookies.set(LANGFLOW_ACCESS_TOKEN, tokens.access_token, { path: "/" });
      if (tokens.refresh_token) {
        cookies.set(LANGFLOW_REFRESH_TOKEN, tokens.refresh_token, { path: "/" });
      }

      console.log('✅ Firebase login successful, navigating to home...');
      // Navigate to home page
      navigate("/");
    } catch (error: any) {
      console.error('Firebase sign-in failed:', error);
      setErrorData({
        title: SIGNIN_ERROR_ALERT,
        list: [error.message || "Firebase authentication failed"],
      });
    }
  }

  return (
    <Form.Root
      onSubmit={(event) => {
        event.preventDefault(); // Prevent default form submission immediately
        console.log('🔄 LoginPage: Form onSubmit triggered');
        if (password === "") {
          return;
        }
        signIn();
      }}
      className="h-screen w-full"
    >
      <div className="flex h-full w-full flex-col items-center justify-center bg-muted">
        <div className="flex w-72 flex-col items-center justify-center gap-2">
          <LangflowLogo
            title="Langflow logo"
            className="mb-4 h-10 w-10 scale-[1.5]"
          />
          <span className="mb-6 text-2xl font-semibold text-primary">
            Sign in to Langflow
          </span>
          {isCheckingFirebase && (
            <div className="mb-4 text-sm text-muted-foreground">
              Checking authentication method...
            </div>
          )}
          {!isCheckingFirebase && (
            <>
              <div className="mb-3 w-full">
                <Form.Field name="username">
                  <Form.Label className="data-[invalid]:label-invalid">
                    {isFirebaseAuth ? "Email" : "Username"} <span className="font-medium text-destructive">*</span>
                  </Form.Label>

                  <Form.Control asChild>
                    <Input
                      type={isFirebaseAuth ? "email" : "username"}
                      onChange={({ target: { value } }) => {
                        handleInput({ target: { name: "username", value } });
                      }}
                      value={username}
                      className="w-full"
                      required
                      placeholder={isFirebaseAuth ? "Email" : "Username"}
                    />
                  </Form.Control>

                  <Form.Message match="valueMissing" className="field-invalid">
                    Please enter your {isFirebaseAuth ? "email" : "username"}
                  </Form.Message>
                </Form.Field>
              </div>
          <div className="mb-3 w-full">
            <Form.Field name="password">
              <Form.Label className="data-[invalid]:label-invalid">
                Password <span className="font-medium text-destructive">*</span>
              </Form.Label>

              <InputComponent
                onChange={(value) => {
                  handleInput({ target: { name: "password", value } });
                }}
                value={password}
                isForm
                password={true}
                required
                placeholder="Password"
                className="w-full"
              />

              <Form.Message className="field-invalid" match="valueMissing">
                Please enter your password
              </Form.Message>
            </Form.Field>
          </div>
              <div className="w-full">
                <Form.Submit asChild>
                  <Button className="mr-3 mt-6 w-full" type="submit" disabled={isCheckingFirebase}>
                    {isCheckingFirebase ? "Loading..." : "Sign in"}
                  </Button>
                </Form.Submit>
              </div>
              <div className="w-full">
                <CustomLink to="/signup">
                  <Button className="w-full" variant="outline" type="button">
                    Don't have an account?&nbsp;<b>Sign Up</b>
                  </Button>
                </CustomLink>
              </div>
              {isFirebaseAuth && (
                <div className="mt-4 text-xs text-muted-foreground text-center">
                  Using Firebase Authentication
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Form.Root>
  );
}
