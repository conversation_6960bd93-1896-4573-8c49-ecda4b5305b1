from langflow.api.v1.ai_assistant import router as ai_assistant_router
from langflow.api.v1.api_key import router as api_key_router
from langflow.api.v1.config import router as config_router
from langflow.api.v1.book import books_router, templates_router, export_router, publish_router, collaboration_router
from langflow.api.v1.video_editor import (
    video_projects_router,
    video_tracks_router,
    video_assets_router,
    video_clips_router,
    video_templates_router,
)
from langflow.api.v1.chat import router as chat_router
from langflow.api.v1.endpoints import router as endpoints_router
from langflow.api.v1.files import router as files_router
from langflow.api.v1.flows import router as flows_router
from langflow.api.v1.folders import router as folders_router
from langflow.api.v1.login import router as login_router
from langflow.api.v1.mcp import router as mcp_router
from langflow.api.v1.social_media import social_media_router
from langflow.api.v1.mcp_projects import router as mcp_projects_router
from langflow.api.v1.monitor import router as monitor_router
from langflow.api.v1.projects import router as projects_router
from langflow.api.v1.starter_projects import router as starter_projects_router
from langflow.api.v1.store import router as store_router
from langflow.api.v1.users import router as users_router
from langflow.api.v1.firebase_users import router as firebase_users_router
from langflow.api.v1.firebase_auth import router as firebase_auth_router
from langflow.api.v1.firebase_design import router as firebase_design_router
from langflow.api.v1.collaboration import router as collaboration_router
from langflow.api.v1.validate import router as validate_router
from langflow.api.v1.workspaces import router as workspaces_router
from langflow.api.v1.firebase_workspaces import router as firebase_workspaces_router
from langflow.api.v1.variable import router as variables_router
from langflow.api.v1.firebase_variables import router as firebase_variables_router
from langflow.api.v1.firebase_books import router as firebase_books_router
from langflow.api.v1.firebase_book_templates import router as firebase_book_templates_router
from langflow.api.v1.firebase_project_management import router as firebase_project_management_router
from langflow.api.v1.firebase_calendar import router as firebase_calendar_router
from langflow.api.v1.firebase_todos import router as firebase_todos_router
from langflow.api.v1.firebase_dashboard import router as firebase_dashboard_router
from langflow.api.v1.voice_mode import router as voice_mode_router
from langflow.api.v1.workspace_members import router as workspace_members_router
# from langflow.api.v1.client_context import router as client_context_router
from langflow.api.v1.roles import router as roles_router
from langflow.api.v1.interactions import router as interactions_router
from langflow.api.v1.email_integration import router as email_integration_router

__all__ = [
    "ai_assistant_router",
    "api_key_router",
    "config_router",
    "books_router",
    "chat_router",
    "endpoints_router",
    "files_router",
    "flows_router",
    "folders_router",
    "login_router",
    "mcp_projects_router",
    "mcp_router",
    "monitor_router",
    "projects_router",
    "starter_projects_router",
    "store_router",
    "templates_router",
    "export_router",
    "publish_router",
    "collaboration_router",
    "video_projects_router",
    "video_tracks_router",
    "video_assets_router",
    "video_clips_router",
    "video_templates_router",
    "users_router",
    "firebase_users_router",
    "firebase_auth_router",
    "firebase_design_router",
    "collaboration_router",
    "validate_router",
    "workspaces_router",
    "firebase_workspaces_router",
    "variables_router",
    "firebase_variables_router",
    "firebase_books_router",
    "firebase_book_templates_router",
    "firebase_project_management_router",
    "firebase_calendar_router",
    "firebase_todos_router",
    "firebase_dashboard_router",
    "voice_mode_router",
    "workspace_members_router",
    # "client_context_router",
    "roles_router",
    "interactions_router",
    "email_integration_router",
]
