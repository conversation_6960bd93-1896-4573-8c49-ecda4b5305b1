"""Email integration API endpoints."""

from typing import Dict, Any
from fastapi import APIRouter, Depends, Request, HTTPException, Query
from fastapi.responses import PlainTextResponse

from langflow.api.utils import CurrentActiveUser
from langflow.services.database.providers.firebase_crm_service import get_firebase_crm_service
from langflow.services.email_integration.email_service import EmailIntegrationService
from langflow.services.email_integration.webhook_handlers import get_webhook_handler
from langflow.logging import logger

router = APIRouter(prefix="/email-integration", tags=["Email Integration"], redirect_slashes=False)


@router.post("/webhook/{provider}")
async def handle_email_webhook(
    provider: str,
    request: Request,
    workspace_id: str = Query(..., description="Workspace ID"),
    secret: str = Query(None, description="Webhook secret for verification"),
    firebase_service=Depends(get_firebase_crm_service)
):
    """Handle email webhooks from various providers."""
    try:
        # Initialize email service
        email_service = EmailIntegrationService(firebase_service)
        
        # Get appropriate webhook handler
        webhook_handler = get_webhook_handler(provider, email_service)
        
        # Handle webhook
        result = await webhook_handler.handle_webhook(request, workspace_id, secret or "")
        
        # Special handling for validation tokens (Outlook)
        if "validation_token" in result:
            return PlainTextResponse(result["validation_token"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email webhook handling failed for provider {provider}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Webhook processing failed: {str(e)}")


@router.post("/manual-process")
async def manually_process_email(
    current_user: CurrentActiveUser,
    email_data: Dict[str, Any],
    workspace_id: str,
    firebase_service=Depends(get_firebase_crm_service)
):
    """Manually process an email for testing purposes."""
    try:
        # Verify workspace access
        from langflow.services.utils import verify_workspace_access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # Initialize email service
        email_service = EmailIntegrationService(firebase_service)
        
        # Process email
        communication = await email_service.process_incoming_email(email_data, workspace_id)
        
        if communication:
            return {
                "success": True,
                "communication_id": communication.id,
                "message": "Email processed successfully"
            }
        else:
            return {
                "success": False,
                "message": "No matching contact found or processing failed"
            }
            
    except Exception as e:
        logger.error(f"Manual email processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/providers")
async def get_supported_providers():
    """Get list of supported email providers."""
    return {
        "providers": [
            {
                "name": "gmail",
                "display_name": "Gmail",
                "webhook_path": "/api/v1/email-integration/webhook/gmail",
                "requires_auth": True,
                "supports_inbound": True,
                "supports_outbound": False
            },
            {
                "name": "outlook",
                "display_name": "Outlook",
                "webhook_path": "/api/v1/email-integration/webhook/outlook",
                "requires_auth": True,
                "supports_inbound": True,
                "supports_outbound": False
            },
            {
                "name": "sendgrid",
                "display_name": "SendGrid",
                "webhook_path": "/api/v1/email-integration/webhook/sendgrid",
                "requires_auth": False,
                "supports_inbound": False,
                "supports_outbound": True
            },
            {
                "name": "mailgun",
                "display_name": "Mailgun",
                "webhook_path": "/api/v1/email-integration/webhook/mailgun",
                "requires_auth": False,
                "supports_inbound": True,
                "supports_outbound": True
            },
            {
                "name": "generic",
                "display_name": "Generic",
                "webhook_path": "/api/v1/email-integration/webhook/generic",
                "requires_auth": False,
                "supports_inbound": True,
                "supports_outbound": True
            }
        ]
    }


@router.post("/test-parsing")
async def test_email_parsing(
    current_user: CurrentActiveUser,
    email_data: Dict[str, Any],
    firebase_service=Depends(get_firebase_crm_service)
):
    """Test email parsing without creating communication log."""
    try:
        # Initialize email service
        email_service = EmailIntegrationService(firebase_service)
        
        # Parse email
        parsed_email = await email_service.parse_email(email_data)
        
        return {
            "success": True,
            "parsed_email": {
                "sender_email": parsed_email.sender_email,
                "sender_name": parsed_email.sender_name,
                "recipient_emails": parsed_email.recipient_emails,
                "subject": parsed_email.subject,
                "body_preview": parsed_email.body[:200] + "..." if len(parsed_email.body) > 200 else parsed_email.body,
                "attachments": parsed_email.attachments,
                "message_id": parsed_email.message_id,
                "in_reply_to": parsed_email.in_reply_to,
                "references": parsed_email.references,
                "timestamp": parsed_email.timestamp.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Email parsing test failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/integration-status/{workspace_id}")
async def get_integration_status(
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_crm_service)
):
    """Get email integration status for workspace."""
    try:
        # Verify workspace access
        from langflow.services.utils import verify_workspace_access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # This would typically check database for configured integrations
        # For now, return basic status
        return {
            "workspace_id": workspace_id,
            "integrations": {
                "gmail": {"configured": False, "last_sync": None},
                "outlook": {"configured": False, "last_sync": None},
                "sendgrid": {"configured": False, "last_sync": None},
                "mailgun": {"configured": False, "last_sync": None}
            },
            "webhook_urls": {
                "gmail": f"/api/v1/email-integration/webhook/gmail?workspace_id={workspace_id}",
                "outlook": f"/api/v1/email-integration/webhook/outlook?workspace_id={workspace_id}",
                "sendgrid": f"/api/v1/email-integration/webhook/sendgrid?workspace_id={workspace_id}",
                "mailgun": f"/api/v1/email-integration/webhook/mailgun?workspace_id={workspace_id}"
            }
        }
        
    except Exception as e:
        logger.error(f"Integration status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))