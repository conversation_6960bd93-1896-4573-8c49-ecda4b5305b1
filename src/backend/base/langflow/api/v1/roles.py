from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query
from langflow.api.utils import CurrentActiveUser
from langflow.services.database.providers.firebase_role_service import FirebaseRoleService
from langflow.services.database.models.crm.role_hierarchy import (
    CompanyRoleDefinition,
    CompanyRoleDefinitionCreate,
    CompanyRoleDefinitionUpdate,
    RoleAssignment,
    RoleAssignmentCreate,
    RoleAssignmentUpdate,
    RoleInheritanceRule,
    RoleInheritanceRuleCreate,
    RoleHierarchyResponse
)

router = APIRouter(prefix="/roles", tags=["roles"])


@router.post("/definitions", response_model=CompanyRoleDefinition)
async def create_role_definition(
    current_user: CurrentActiveUser,
    role_data: CompanyRoleDefinitionCreate,
    workspace_id: str = Query(...)
):
    """Create a new company role definition"""
    service = FirebaseRoleService()
    try:
        return await service.create_role_definition(role_data, workspace_id, current_user.id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create role definition: {str(e)}")


@router.get("/definitions", response_model=List[CompanyRoleDefinition])
async def list_role_definitions(
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...),
    company_id: Optional[str] = Query(None),
    active_only: bool = Query(True)
):
    """List role definitions for a workspace"""
    service = FirebaseRoleService()
    try:
        return await service.list_role_definitions(workspace_id, company_id, active_only)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list role definitions: {str(e)}")


@router.get("/definitions/{role_id}", response_model=CompanyRoleDefinition)
async def get_role_definition(
    role_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Get a specific role definition"""
    service = FirebaseRoleService()
    try:
        role = await service.get_role_definition(role_id, workspace_id)
        if not role:
            raise HTTPException(status_code=404, detail="Role definition not found")
        return role
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get role definition: {str(e)}")


@router.patch("/definitions/{role_id}", response_model=CompanyRoleDefinition)
async def update_role_definition(
    role_id: str,
    current_user: CurrentActiveUser,
    role_data: CompanyRoleDefinitionUpdate,
    workspace_id: str = Query(...)
):
    """Update a role definition"""
    service = FirebaseRoleService()
    try:
        role = await service.update_role_definition(role_id, role_data, workspace_id)
        if not role:
            raise HTTPException(status_code=404, detail="Role definition not found")
        return role
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update role definition: {str(e)}")


@router.delete("/definitions/{role_id}")
async def delete_role_definition(
    role_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Delete a role definition"""
    service = FirebaseRoleService()
    try:
        success = await service.delete_role_definition(role_id, workspace_id)
        if not success:
            raise HTTPException(status_code=404, detail="Role definition not found")
        return {"message": "Role definition deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete role definition: {str(e)}")


@router.get("/hierarchy/{company_id}", response_model=List[RoleHierarchyResponse])
async def get_role_hierarchy(
    company_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Get the complete role hierarchy for a company"""
    service = FirebaseRoleService()
    try:
        return await service.get_role_hierarchy(workspace_id, company_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get role hierarchy: {str(e)}")


@router.post("/assignments", response_model=RoleAssignment)
async def assign_role(
    current_user: CurrentActiveUser,
    assignment_data: RoleAssignmentCreate,
    workspace_id: str = Query(...)
):
    """Assign a role to a contact"""
    service = FirebaseRoleService()
    try:
        return await service.assign_role(assignment_data, workspace_id, current_user.id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to assign role: {str(e)}")


@router.get("/assignments", response_model=List[RoleAssignment])
async def list_role_assignments(
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...),
    contact_id: Optional[str] = Query(None),
    company_id: Optional[str] = Query(None),
    role_definition_id: Optional[str] = Query(None),
    current_only: bool = Query(False)
):
    """List role assignments with optional filters"""
    service = FirebaseRoleService()
    try:
        return await service.list_role_assignments(
            workspace_id, contact_id, company_id, role_definition_id, current_only
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list role assignments: {str(e)}")


@router.patch("/assignments/{assignment_id}", response_model=RoleAssignment)
async def update_role_assignment(
    assignment_id: str,
    current_user: CurrentActiveUser,
    assignment_data: RoleAssignmentUpdate,
    workspace_id: str = Query(...)
):
    """Update a role assignment"""
    service = FirebaseRoleService()
    try:
        assignment = await service.update_role_assignment(assignment_id, assignment_data, workspace_id)
        if not assignment:
            raise HTTPException(status_code=404, detail="Role assignment not found")
        return assignment
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update role assignment: {str(e)}")


@router.post("/inheritance-rules", response_model=RoleInheritanceRule)
async def create_inheritance_rule(
    current_user: CurrentActiveUser,
    rule_data: RoleInheritanceRuleCreate,
    workspace_id: str = Query(...)
):
    """Create a role inheritance rule"""
    service = FirebaseRoleService()
    try:
        return await service.create_inheritance_rule(rule_data, workspace_id, current_user.id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create inheritance rule: {str(e)}")


@router.get("/inheritance-rules", response_model=List[RoleInheritanceRule])
async def list_inheritance_rules(
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...),
    company_id: Optional[str] = Query(None)
):
    """List inheritance rules for a workspace/company"""
    service = FirebaseRoleService()
    try:
        return await service.list_inheritance_rules(workspace_id, company_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list inheritance rules: {str(e)}")


@router.get("/authority/{contact_id}/{company_id}")
async def get_effective_authority(
    contact_id: str,
    company_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Calculate the effective authority level for a contact in a company"""
    service = FirebaseRoleService()
    try:
        return await service.calculate_effective_authority(contact_id, company_id, workspace_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to calculate authority: {str(e)}")


@router.get("/decision-makers/{contact_id}/{company_id}")
async def get_decision_makers(
    contact_id: str,
    company_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Get all decision makers who can influence this contact"""
    service = FirebaseRoleService()
    try:
        return await service.get_decision_makers_for_contact(contact_id, company_id, workspace_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get decision makers: {str(e)}")


@router.get("/templates")
async def get_role_templates():
    """Get predefined role templates for common organizational structures"""
    templates = {
        "technology_company": [
            {
                "role_name": "Chief Executive Officer",
                "role_code": "CEO",
                "hierarchy_level": 1,
                "decision_authority_level": 10,
                "influence_scope": "company",
                "is_leadership_role": True,
                "is_decision_maker": True,
                "is_influencer": True,
                "can_approve_budget": True,
                "can_hire": True
            },
            {
                "role_name": "Chief Technology Officer",
                "role_code": "CTO",
                "hierarchy_level": 2,
                "decision_authority_level": 9,
                "influence_scope": "division",
                "is_leadership_role": True,
                "is_decision_maker": True,
                "is_influencer": True,
                "can_approve_budget": True,
                "can_hire": True
            },
            {
                "role_name": "Engineering Director",
                "role_code": "ENG_DIR",
                "hierarchy_level": 3,
                "decision_authority_level": 8,
                "influence_scope": "department",
                "is_leadership_role": True,
                "is_decision_maker": True,
                "is_influencer": True,
                "can_approve_budget": True,
                "can_hire": True
            },
            {
                "role_name": "Engineering Manager",
                "role_code": "ENG_MGR",
                "hierarchy_level": 4,
                "decision_authority_level": 6,
                "influence_scope": "department",
                "is_leadership_role": True,
                "is_decision_maker": False,
                "is_influencer": True,
                "can_approve_budget": False,
                "can_hire": True
            },
            {
                "role_name": "Senior Engineer",
                "role_code": "SR_ENG",
                "hierarchy_level": 5,
                "decision_authority_level": 4,
                "influence_scope": "department",
                "is_leadership_role": False,
                "is_decision_maker": False,
                "is_influencer": True,
                "can_approve_budget": False,
                "can_hire": False
            }
        ],
        "sales_organization": [
            {
                "role_name": "Chief Revenue Officer",
                "role_code": "CRO",
                "hierarchy_level": 1,
                "decision_authority_level": 10,
                "influence_scope": "company",
                "is_leadership_role": True,
                "is_decision_maker": True,
                "is_influencer": True,
                "can_approve_budget": True,
                "can_hire": True
            },
            {
                "role_name": "VP of Sales",
                "role_code": "VP_SALES",
                "hierarchy_level": 2,
                "decision_authority_level": 9,
                "influence_scope": "division",
                "is_leadership_role": True,
                "is_decision_maker": True,
                "is_influencer": True,
                "can_approve_budget": True,
                "can_hire": True
            },
            {
                "role_name": "Sales Director",
                "role_code": "SALES_DIR",
                "hierarchy_level": 3,
                "decision_authority_level": 7,
                "influence_scope": "department",
                "is_leadership_role": True,
                "is_decision_maker": True,
                "is_influencer": True,
                "can_approve_budget": True,
                "can_hire": True
            },
            {
                "role_name": "Sales Manager",
                "role_code": "SALES_MGR",
                "hierarchy_level": 4,
                "decision_authority_level": 5,
                "influence_scope": "department",
                "is_leadership_role": True,
                "is_decision_maker": False,
                "is_influencer": True,
                "can_approve_budget": False,
                "can_hire": False
            },
            {
                "role_name": "Account Executive",
                "role_code": "AE",
                "hierarchy_level": 5,
                "decision_authority_level": 3,
                "influence_scope": "department",
                "is_leadership_role": False,
                "is_decision_maker": False,
                "is_influencer": False,
                "can_approve_budget": False,
                "can_hire": False
            }
        ]
    }
    
    return {"templates": templates}