from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from langflow.api.utils import CurrentActiveUser
from langflow.services.database.providers.firebase_interaction_service import FirebaseInteractionService
from langflow.services.database.models.crm.interaction_tracking import (
    CommunicationLog,
    CommunicationLogCreate,
    CommunicationLogUpdate,
    InteractionTemplate,
    InteractionTemplateCreate,
    InteractionAnalytics,
    InteractionType,
    InteractionDirection,
    InteractionSentiment
)

router = APIRouter(prefix="/interactions", tags=["interactions"])


@router.post("/logs", response_model=CommunicationLog)
async def log_interaction(
    current_user: CurrentActiveUser,
    log_data: CommunicationLogCreate,
    workspace_id: str = Query(...)
):
    """Log a new communication interaction"""
    service = FirebaseInteractionService()
    try:
        return await service.log_interaction(log_data, workspace_id, current_user.id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to log interaction: {str(e)}")


@router.get("/logs", response_model=List[CommunicationLog])
async def list_interaction_logs(
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...),
    contact_id: Optional[str] = Query(None),
    relationship_id: Optional[str] = Query(None),
    interaction_type: Optional[InteractionType] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0)
):
    """List communication logs with optional filters"""
    service = FirebaseInteractionService()
    try:
        return await service.list_interaction_logs(
            workspace_id, contact_id, relationship_id, interaction_type,
            start_date, end_date, limit, offset
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list interaction logs: {str(e)}")


@router.get("/logs/{log_id}", response_model=CommunicationLog)
async def get_interaction_log(
    log_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Get a specific communication log"""
    service = FirebaseInteractionService()
    try:
        log = await service.get_interaction_log(log_id, workspace_id)
        if not log:
            raise HTTPException(status_code=404, detail="Interaction log not found")
        return log
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get interaction log: {str(e)}")


@router.patch("/logs/{log_id}", response_model=CommunicationLog)
async def update_interaction_log(
    log_id: str,
    current_user: CurrentActiveUser,
    log_data: CommunicationLogUpdate,
    workspace_id: str = Query(...)
):
    """Update a communication log"""
    service = FirebaseInteractionService()
    try:
        log = await service.update_interaction_log(log_id, log_data, workspace_id)
        if not log:
            raise HTTPException(status_code=404, detail="Interaction log not found")
        return log
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update interaction log: {str(e)}")


@router.delete("/logs/{log_id}")
async def delete_interaction_log(
    log_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Delete a communication log"""
    service = FirebaseInteractionService()
    try:
        success = await service.delete_interaction_log(log_id, workspace_id)
        if not success:
            raise HTTPException(status_code=404, detail="Interaction log not found")
        return {"message": "Interaction log deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete interaction log: {str(e)}")


@router.get("/analytics", response_model=InteractionAnalytics)
async def get_interaction_analytics(
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...),
    contact_id: Optional[str] = Query(None),
    relationship_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None)
):
    """Get comprehensive interaction analytics"""
    service = FirebaseInteractionService()
    try:
        return await service.get_interaction_analytics(
            workspace_id, contact_id, relationship_id, start_date, end_date
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get interaction analytics: {str(e)}")


@router.post("/templates", response_model=InteractionTemplate)
async def create_interaction_template(
    current_user: CurrentActiveUser,
    template_data: InteractionTemplateCreate,
    workspace_id: str = Query(...)
):
    """Create a new interaction template"""
    service = FirebaseInteractionService()
    try:
        return await service.create_interaction_template(template_data, workspace_id, current_user.id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create interaction template: {str(e)}")


@router.get("/templates", response_model=List[InteractionTemplate])
async def list_interaction_templates(
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...),
    interaction_type: Optional[InteractionType] = Query(None)
):
    """List interaction templates"""
    service = FirebaseInteractionService()
    try:
        return await service.list_interaction_templates(workspace_id, interaction_type)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list interaction templates: {str(e)}")


@router.post("/templates/{template_id}/use", response_model=InteractionTemplate)
async def use_interaction_template(
    template_id: str,
    current_user: CurrentActiveUser,
    workspace_id: str = Query(...)
):
    """Use an interaction template (increments usage count)"""
    service = FirebaseInteractionService()
    try:
        template = await service.use_interaction_template(template_id, workspace_id)
        if not template:
            raise HTTPException(status_code=404, detail="Interaction template not found")
        return template
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to use interaction template: {str(e)}")


@router.get("/types")
async def get_interaction_types():
    """Get all available interaction types"""
    return {
        "interaction_types": [
            {
                "value": interaction_type.value,
                "label": interaction_type.value.replace('_', ' ').title(),
                "category": _get_interaction_category(interaction_type)
            }
            for interaction_type in InteractionType
        ],
        "directions": [
            {
                "value": direction.value,
                "label": direction.value.replace('_', ' ').title()
            }
            for direction in InteractionDirection
        ],
        "sentiments": [
            {
                "value": sentiment.value,
                "label": sentiment.value.replace('_', ' ').title(),
                "score": _get_sentiment_score(sentiment)
            }
            for sentiment in InteractionSentiment
        ]
    }


@router.get("/quick-log-templates")
async def get_quick_log_templates():
    """Get predefined quick log templates for common interactions"""
    templates = {
        "email_follow_up": {
            "interaction_type": "email",
            "interaction_direction": "outbound",
            "subject": "Follow-up: {previous_topic}",
            "default_topics": ["follow_up", "next_steps"],
            "default_importance_level": 3,
            "requires_follow_up": True
        },
        "discovery_call": {
            "interaction_type": "phone_call",
            "interaction_direction": "mutual",
            "subject": "Discovery Call",
            "default_topics": ["needs_assessment", "product_demo", "qualification"],
            "default_duration_minutes": 30,
            "default_importance_level": 4,
            "requires_follow_up": True
        },
        "proposal_presentation": {
            "interaction_type": "meeting",
            "interaction_direction": "outbound",
            "subject": "Proposal Presentation",
            "default_topics": ["proposal", "pricing", "timeline"],
            "default_duration_minutes": 60,
            "default_importance_level": 5,
            "requires_follow_up": True
        },
        "check_in_call": {
            "interaction_type": "phone_call",
            "interaction_direction": "outbound",
            "subject": "Check-in Call",
            "default_topics": ["relationship_maintenance", "updates"],
            "default_duration_minutes": 15,
            "default_importance_level": 2,
            "requires_follow_up": False
        },
        "linkedin_connection": {
            "interaction_type": "linkedin",
            "interaction_direction": "outbound",
            "subject": "LinkedIn Connection",
            "default_topics": ["networking", "introduction"],
            "default_importance_level": 2,
            "requires_follow_up": True
        }
    }
    
    return {"templates": templates}


def _get_interaction_category(interaction_type: InteractionType) -> str:
    """Categorize interaction types"""
    communication_types = [InteractionType.EMAIL, InteractionType.PHONE_CALL, 
                          InteractionType.TEXT_MESSAGE, InteractionType.VIDEO_CALL]
    social_types = [InteractionType.SOCIAL_MEDIA, InteractionType.LINKEDIN]
    business_types = [InteractionType.MEETING, InteractionType.PROPOSAL_SENT, 
                     InteractionType.PROPOSAL_RECEIVED, InteractionType.CONTRACT_SIGNED]
    
    if interaction_type in communication_types:
        return "communication"
    elif interaction_type in social_types:
        return "social"
    elif interaction_type in business_types:
        return "business"
    else:
        return "other"


def _get_sentiment_score(sentiment: InteractionSentiment) -> float:
    """Convert sentiment to numeric score"""
    sentiment_scores = {
        InteractionSentiment.VERY_NEGATIVE: -1.0,
        InteractionSentiment.NEGATIVE: -0.5,
        InteractionSentiment.NEUTRAL: 0.0,
        InteractionSentiment.POSITIVE: 0.5,
        InteractionSentiment.VERY_POSITIVE: 1.0
    }
    return sentiment_scores.get(sentiment, 0.0)