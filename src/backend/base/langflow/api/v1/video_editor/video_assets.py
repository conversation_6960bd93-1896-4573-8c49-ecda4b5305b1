from datetime import datetime, timezone
from typing import Dict, List, Optional
from uuid import UUID
import os
from pathlib import Path

from fastapi import APIRouter, HTTPException, Query, UploadFile, File, Form
from sqlalchemy.exc import IntegrityError
from sqlmodel import select

from langflow.api.utils import CurrentActiveUser, DbSession
from langflow.services.database.models.video_editor.video_asset import (
    VideoAsset,
    VideoAssetCreate,
    VideoAssetRead,
    VideoAssetUpdate,
)
from langflow.services.database.models.video_editor.video_project import VideoProject
from langflow.services.video_processing import video_processing_service
from langflow.services.video_processing.video_tasks import process_video_upload, generate_video_previews
from langflow.logging.logger import logger

router = APIRouter(tags=["Video Assets"], prefix="/video-assets")


@router.post("/upload", response_model=VideoAssetRead, status_code=201)
async def upload_video_asset(
    project_id: str = Form(...),
    file: UploadFile = File(...),
    session: DbSession = None,
    current_user: CurrentActiveUser = None,
) -> VideoAsset:
    """Upload and process a video file."""
    # Verify the project belongs to the user
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Validate file type
    if not file.content_type or not file.content_type.startswith("video/"):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Check if format is supported
    file_extension = Path(file.filename).suffix.lower().lstrip(".")
    if not video_processing_service.is_format_supported(f"test.{file_extension}"):
        raise HTTPException(
            status_code=400, 
            detail=f"Video format '{file_extension}' is not supported"
        )
    
    # Create upload directory structure
    upload_dir = Path("uploads") / "video_assets" / str(project_id)
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # Save uploaded file
    file_path = upload_dir / file.filename
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    try:
        # Validate video file and get basic metadata
        metadata = await video_processing_service.validate_video_file(str(file_path))
        
        # Create video asset record
        asset_data = VideoAssetCreate(
            project_id=project_id,
            asset_type="video",
            file_path=str(file_path),
            file_name=file.filename,
            file_size=len(content),
            duration=metadata["format"]["duration"],
            asset_metadata=metadata,
            mime_type=file.content_type
        )
        
        new_asset = VideoAsset.model_validate(asset_data, from_attributes=True)
        session.add(new_asset)
        await session.commit()
        await session.refresh(new_asset)
        
        # Start background processing
        task = process_video_upload.delay(
            video_asset_id=str(new_asset.id),
            input_path=str(file_path),
            user_id=str(current_user.id),
            workspace_id=getattr(current_user, 'workspace_id', None)
        )
        
        logger.info(f"Started video processing task {task.id} for asset {new_asset.id}")
        
        return new_asset
        
    except Exception as e:
        # Clean up uploaded file on error
        if file_path.exists():
            file_path.unlink()
        
        logger.error(f"Video upload failed: {e}")
        raise HTTPException(status_code=400, detail=f"Video upload failed: {e}")


@router.post("/", response_model=VideoAssetRead, status_code=201)
async def create_video_asset(
    asset: VideoAssetCreate,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> VideoAsset:
    """Create a new video asset."""
    # Verify the project belongs to the user
    project_query = select(VideoProject).where(
        VideoProject.id == asset.project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    new_asset = VideoAsset.model_validate(asset, from_attributes=True)
    try:
        session.add(new_asset)
        await session.commit()
        await session.refresh(new_asset)
    except IntegrityError as e:
        await session.rollback()
        raise HTTPException(status_code=400, detail=f"Error creating video asset: {e}") from e

    return new_asset


@router.get("/project/{project_id}", response_model=List[VideoAssetRead])
async def get_video_assets_by_project(
    project_id: UUID,
    session: DbSession,
    current_user: CurrentActiveUser,
    asset_type: str | None = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
) -> List[VideoAsset]:
    """Get video assets for a specific project."""
    # Verify the project belongs to the user
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    query = select(VideoAsset).where(VideoAsset.project_id == project_id)
    
    if asset_type:
        query = query.where(VideoAsset.asset_type == asset_type)
    
    query = query.offset(skip).limit(limit).order_by(VideoAsset.created_at.desc())
    
    result = await session.exec(query)
    return result.all()


@router.get("/{asset_id}", response_model=VideoAssetRead)
async def get_video_asset(
    asset_id: UUID,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> VideoAsset:
    """Get a specific video asset."""
    query = select(VideoAsset).join(VideoProject).where(
        VideoAsset.id == asset_id,
        VideoProject.user_id == current_user.id
    )
    result = await session.exec(query)
    asset = result.first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Video asset not found")
    
    return asset


@router.put("/{asset_id}", response_model=VideoAssetRead)
async def update_video_asset(
    asset_id: UUID,
    asset_update: VideoAssetUpdate,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> VideoAsset:
    """Update a video asset."""
    query = select(VideoAsset).join(VideoProject).where(
        VideoAsset.id == asset_id,
        VideoProject.user_id == current_user.id
    )
    result = await session.exec(query)
    asset = result.first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Video asset not found")
    
    update_data = asset_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(asset, field, value)
    
    asset.updated_at = datetime.now(timezone.utc)
    
    try:
        await session.commit()
        await session.refresh(asset)
    except IntegrityError as e:
        await session.rollback()
        raise HTTPException(status_code=400, detail=f"Error updating video asset: {e}") from e
    
    return asset


@router.delete("/{asset_id}", status_code=204)
async def delete_video_asset(
    asset_id: UUID,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> None:
    """Delete a video asset."""
    query = select(VideoAsset).join(VideoProject).where(
        VideoAsset.id == asset_id,
        VideoProject.user_id == current_user.id
    )
    result = await session.exec(query)
    asset = result.first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Video asset not found")
    
    await session.delete(asset)
    await session.commit()


@router.get("/{asset_id}/metadata", response_model=Dict)
async def get_video_metadata(
    asset_id: UUID,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Get detailed video metadata."""
    query = select(VideoAsset).join(VideoProject).where(
        VideoAsset.id == asset_id,
        VideoProject.user_id == current_user.id
    )
    result = await session.exec(query)
    asset = result.first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Video asset not found")
    
    if asset.asset_type != "video":
        raise HTTPException(status_code=400, detail="Asset is not a video file")
    
    try:
        # Get fresh metadata from the video file
        metadata = await video_processing_service.validate_video_file(asset.file_path)
        return metadata
    except Exception as e:
        logger.error(f"Failed to get metadata for asset {asset_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve video metadata")


@router.post("/{asset_id}/generate-previews")
async def generate_asset_previews(
    asset_id: UUID,
    preview_count: int = Query(default=10, ge=5, le=50),
    session: DbSession = None,
    current_user: CurrentActiveUser = None,
) -> Dict:
    """Generate preview thumbnails for video scrubbing."""
    query = select(VideoAsset).join(VideoProject).where(
        VideoAsset.id == asset_id,
        VideoProject.user_id == current_user.id
    )
    result = await session.exec(query)
    asset = result.first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Video asset not found")
    
    if asset.asset_type != "video":
        raise HTTPException(status_code=400, detail="Asset is not a video file")
    
    # Start background task for preview generation
    task = generate_video_previews.delay(
        video_asset_id=str(asset_id),
        input_path=asset.file_path,
        preview_count=preview_count
    )
    
    return {
        "task_id": task.id,
        "status": "started",
        "asset_id": str(asset_id),
        "preview_count": preview_count
    }


@router.get("/{asset_id}/processing-status")
async def get_processing_status(
    asset_id: UUID,
    task_id: Optional[str] = Query(None),
    session: DbSession = None,
    current_user: CurrentActiveUser = None,
) -> Dict:
    """Get the processing status of a video asset."""
    query = select(VideoAsset).join(VideoProject).where(
        VideoAsset.id == asset_id,
        VideoProject.user_id == current_user.id
    )
    result = await session.exec(query)
    asset = result.first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Video asset not found")
    
    # If task_id provided, get specific task status
    if task_id:
        from langflow.core.celery_app import celery_app as celery
        task_result = celery.AsyncResult(task_id)
        
        return {
            "asset_id": str(asset_id),
            "task_id": task_id,
            "status": task_result.status,
            "result": task_result.result if task_result.ready() else None,
            "meta": task_result.info
        }
    
    # Otherwise return general asset processing status
    return {
        "asset_id": str(asset_id),
        "file_path": asset.file_path,
        "file_exists": Path(asset.file_path).exists(),
        "metadata": asset.asset_metadata,
        "created_at": asset.created_at.isoformat(),
        "updated_at": asset.updated_at.isoformat()
    }


@router.get("/{asset_id}/thumbnail")
async def get_video_thumbnail(
    asset_id: UUID,
    timestamp: float = Query(default=1.0, ge=0),
    width: int = Query(default=320, ge=64, le=1920),
    height: int = Query(default=240, ge=64, le=1080),
    session: DbSession = None,
    current_user: CurrentActiveUser = None,
) -> Dict:
    """Generate a thumbnail for a video at a specific timestamp."""
    query = select(VideoAsset).join(VideoProject).where(
        VideoAsset.id == asset_id,
        VideoProject.user_id == current_user.id
    )
    result = await session.exec(query)
    asset = result.first()
    
    if not asset:
        raise HTTPException(status_code=404, detail="Video asset not found")
    
    if asset.asset_type != "video":
        raise HTTPException(status_code=400, detail="Asset is not a video file")
    
    try:
        # Create thumbnail directory
        thumbnail_dir = Path(asset.file_path).parent / "thumbnails"
        thumbnail_dir.mkdir(exist_ok=True)
        
        # Generate unique thumbnail filename
        thumb_filename = f"{Path(asset.file_path).stem}_t{timestamp}_{width}x{height}.jpg"
        thumbnail_path = thumbnail_dir / thumb_filename
        
        # Generate thumbnail
        output_path = await video_processing_service.generate_thumbnail(
            input_path=asset.file_path,
            output_path=str(thumbnail_path),
            timestamp=timestamp,
            width=width,
            height=height
        )
        
        return {
            "asset_id": str(asset_id),
            "thumbnail_path": output_path,
            "timestamp": timestamp,
            "dimensions": {"width": width, "height": height},
            "generated_at": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Thumbnail generation failed for asset {asset_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Thumbnail generation failed: {e}")


@router.get("/supported-formats")
async def get_supported_formats() -> Dict:
    """Get list of supported video formats and codecs."""
    return {
        "formats": video_processing_service.get_supported_formats(),
        "codecs": video_processing_service.supported_codecs,
        "ffmpeg_version": await video_processing_service.check_ffmpeg_version()
    }
