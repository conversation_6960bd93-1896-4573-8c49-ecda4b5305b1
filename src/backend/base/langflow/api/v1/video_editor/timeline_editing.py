"""
Timeline Editing API endpoints for professional video editing operations.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from sqlmodel import select

from langflow.api.utils import CurrentActiveUser, DbSession
from langflow.services.database.models.video_editor.video_project import VideoProject
from langflow.services.timeline import (
    timeline_service,
    EditMode,
    TimelineError,
    TimelinePosition,
)
from langflow.logging.logger import logger


router = APIRouter(tags=["Timeline Editing"], prefix="/timeline")


# Request/Response Models
class EditModeRequest(BaseModel):
    """Request to change edit mode."""
    mode: str = Field(..., description="Edit mode: normal, ripple, roll, slip, slide")


class TimelineSettingsRequest(BaseModel):
    """Request to update timeline settings."""
    snap_enabled: Optional[bool] = None
    snap_distance: Optional[int] = None
    magnetic_timeline: Optional[bool] = None
    fps: Optional[float] = None


class RippleEditRequest(BaseModel):
    """Request for ripple edit operation."""
    clip_id: str
    new_in_point: float
    new_out_point: float


class RollEditRequest(BaseModel):
    """Request for roll edit operation."""
    clip_a_id: str
    clip_b_id: str
    new_edit_point: float


class SlipEditRequest(BaseModel):
    """Request for slip edit operation."""
    clip_id: str
    new_asset_start: float
    new_asset_end: float


class SlideEditRequest(BaseModel):
    """Request for slide edit operation."""
    clip_id: str
    new_start_time: float


class SplitClipRequest(BaseModel):
    """Request for split clip operation."""
    clip_id: str
    split_time: float


class TimelineNavigationRequest(BaseModel):
    """Request for timeline navigation."""
    frame: Optional[int] = None
    seconds: Optional[float] = None
    timecode: Optional[str] = None


class ClipMoveRequest(BaseModel):
    """Request to move a clip."""
    clip_id: str
    new_start_time: float
    track_id: Optional[str] = None


class SnapPositionRequest(BaseModel):
    """Request to calculate snap position."""
    position: float
    zoom_level: float = 1.0


# API Endpoints
@router.get("/{project_id}/status")
async def get_timeline_status(
    project_id: UUID,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Get timeline status and settings for a project."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get or create timeline
    timeline = timeline_service.get_timeline(str(project_id))
    if not timeline:
        timeline = timeline_service.create_timeline(str(project_id), fps=30.0)
    
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    
    return {
        "project_id": str(project_id),
        "fps": timeline.fps,
        "current_frame": timeline.current_frame,
        "total_duration": timeline.total_duration,
        "edit_mode": timeline.edit_mode.value,
        "snap_enabled": timeline.snap_enabled,
        "snap_distance": timeline.snap_distance,
        "magnetic_timeline": timeline.magnetic_timeline,
        "clips_count": len(timeline.clips),
        "tracks_count": len(timeline.tracks),
        "can_undo": edit_ops.history_index >= 0 if edit_ops else False,
        "can_redo": edit_ops.history_index < len(edit_ops.edit_history) - 1 if edit_ops else False
    }


@router.post("/{project_id}/settings")
async def update_timeline_settings(
    project_id: UUID,
    settings: TimelineSettingsRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Update timeline settings."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get timeline
    timeline = timeline_service.get_timeline(str(project_id))
    if not timeline:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    # Update settings
    if settings.snap_enabled is not None:
        timeline.snap_enabled = settings.snap_enabled
    if settings.snap_distance is not None:
        timeline.snap_distance = settings.snap_distance
    if settings.magnetic_timeline is not None:
        timeline.magnetic_timeline = settings.magnetic_timeline
    if settings.fps is not None:
        timeline.fps = settings.fps
    
    logger.info(f"Updated timeline settings for project {project_id}")
    
    return {
        "success": True,
        "settings": {
            "snap_enabled": timeline.snap_enabled,
            "snap_distance": timeline.snap_distance,
            "magnetic_timeline": timeline.magnetic_timeline,
            "fps": timeline.fps
        }
    }


@router.post("/{project_id}/edit-mode")
async def set_edit_mode(
    project_id: UUID,
    mode_request: EditModeRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Set the current edit mode."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Validate edit mode
    try:
        edit_mode = EditMode(mode_request.mode)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid edit mode: {mode_request.mode}"
        )
    
    # Get timeline
    timeline = timeline_service.get_timeline(str(project_id))
    if not timeline:
        timeline = timeline_service.create_timeline(str(project_id))
    
    timeline.edit_mode = edit_mode
    
    logger.info(f"Set edit mode to {edit_mode.value} for project {project_id}")
    
    return {
        "success": True,
        "edit_mode": edit_mode.value
    }


@router.post("/{project_id}/navigate")
async def navigate_timeline(
    project_id: UUID,
    navigation: TimelineNavigationRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Navigate to a specific position on the timeline."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get timeline
    timeline = timeline_service.get_timeline(str(project_id))
    if not timeline:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    # Calculate new position
    if navigation.frame is not None:
        timeline.current_frame = navigation.frame
    elif navigation.seconds is not None:
        timeline.current_frame = int(navigation.seconds * timeline.fps)
    elif navigation.timecode is not None:
        # Parse timecode (HH:MM:SS:FF)
        try:
            parts = navigation.timecode.split(":")
            if len(parts) != 4:
                raise ValueError("Invalid timecode format")
            
            hours, minutes, seconds, frames = map(int, parts)
            total_seconds = hours * 3600 + minutes * 60 + seconds
            timeline.current_frame = int(total_seconds * timeline.fps) + frames
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timecode format")
    
    # Create position object
    position = TimelinePosition.from_frames(timeline.current_frame, timeline.fps)
    
    return {
        "success": True,
        "position": {
            "frame": timeline.current_frame,
            "seconds": position.seconds,
            "timecode": position.timecode
        }
    }


@router.post("/{project_id}/ripple-edit")
async def perform_ripple_edit(
    project_id: UUID,
    edit_request: RippleEditRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Perform a ripple edit operation."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    try:
        operation = edit_ops.ripple_edit(
            clip_id=edit_request.clip_id,
            new_in_point=edit_request.new_in_point,
            new_out_point=edit_request.new_out_point
        )
        
        return {
            "success": True,
            "operation_id": operation.id,
            "operation_type": operation.operation_type,
            "affected_clips": operation.affected_clips,
            "timestamp": operation.timestamp.isoformat()
        }
        
    except TimelineError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/{project_id}/roll-edit")
async def perform_roll_edit(
    project_id: UUID,
    edit_request: RollEditRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Perform a roll edit operation."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    try:
        operation = edit_ops.roll_edit(
            clip_a_id=edit_request.clip_a_id,
            clip_b_id=edit_request.clip_b_id,
            new_edit_point=edit_request.new_edit_point
        )
        
        return {
            "success": True,
            "operation_id": operation.id,
            "operation_type": operation.operation_type,
            "affected_clips": operation.affected_clips,
            "timestamp": operation.timestamp.isoformat()
        }
        
    except TimelineError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/{project_id}/slip-edit")
async def perform_slip_edit(
    project_id: UUID,
    edit_request: SlipEditRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Perform a slip edit operation."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    try:
        operation = edit_ops.slip_edit(
            clip_id=edit_request.clip_id,
            new_asset_start=edit_request.new_asset_start,
            new_asset_end=edit_request.new_asset_end
        )
        
        return {
            "success": True,
            "operation_id": operation.id,
            "operation_type": operation.operation_type,
            "timestamp": operation.timestamp.isoformat()
        }
        
    except TimelineError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/{project_id}/slide-edit")
async def perform_slide_edit(
    project_id: UUID,
    edit_request: SlideEditRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Perform a slide edit operation."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    try:
        operation = edit_ops.slide_edit(
            clip_id=edit_request.clip_id,
            new_start_time=edit_request.new_start_time
        )
        
        return {
            "success": True,
            "operation_id": operation.id,
            "operation_type": operation.operation_type,
            "affected_clips": operation.affected_clips,
            "timestamp": operation.timestamp.isoformat()
        }
        
    except TimelineError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/{project_id}/split-clip")
async def split_clip(
    project_id: UUID,
    split_request: SplitClipRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Split a clip at the specified time."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    try:
        operation, new_clip_id = edit_ops.split_clip(
            clip_id=split_request.clip_id,
            split_time=split_request.split_time
        )
        
        return {
            "success": True,
            "operation_id": operation.id,
            "operation_type": operation.operation_type,
            "new_clip_id": new_clip_id,
            "timestamp": operation.timestamp.isoformat()
        }
        
    except TimelineError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/{project_id}/undo")
async def undo_operation(
    project_id: UUID,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Undo the last edit operation."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    success = edit_ops.undo()
    
    if not success:
        raise HTTPException(status_code=400, detail="Nothing to undo")
    
    return {
        "success": True,
        "can_undo": edit_ops.history_index >= 0,
        "can_redo": edit_ops.history_index < len(edit_ops.edit_history) - 1
    }


@router.post("/{project_id}/redo")
async def redo_operation(
    project_id: UUID,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Redo the next edit operation."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    success = edit_ops.redo()
    
    if not success:
        raise HTTPException(status_code=400, detail="Nothing to redo")
    
    return {
        "success": True,
        "can_undo": edit_ops.history_index >= 0,
        "can_redo": edit_ops.history_index < len(edit_ops.edit_history) - 1
    }


@router.post("/{project_id}/snap-position")
async def calculate_snap_position(
    project_id: UUID,
    snap_request: SnapPositionRequest,
    session: DbSession,
    current_user: CurrentActiveUser,
) -> Dict:
    """Calculate the snapped position for magnetic timeline."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get magnetic timeline
    magnetic_timeline = timeline_service.get_magnetic_timeline(str(project_id))
    if not magnetic_timeline:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    snapped_position = magnetic_timeline.calculate_snap_position(
        position=snap_request.position,
        zoom_level=snap_request.zoom_level
    )
    
    return {
        "original_position": snap_request.position,
        "snapped_position": snapped_position,
        "was_snapped": abs(snapped_position - snap_request.position) > 0.001
    }


@router.get("/{project_id}/clips")
async def get_timeline_clips(
    project_id: UUID,
    track_id: Optional[str] = Query(None),
    session: DbSession = None,
    current_user: CurrentActiveUser = None,
) -> Dict:
    """Get all clips on the timeline."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get timeline
    timeline = timeline_service.get_timeline(str(project_id))
    if not timeline:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    if track_id:
        clips = timeline.get_clips_on_track(track_id)
    else:
        clips = list(timeline.clips.values())
    
    return {
        "project_id": str(project_id),
        "track_id": track_id,
        "clips": clips,
        "total_clips": len(clips)
    }


@router.get("/{project_id}/edit-history")
async def get_edit_history(
    project_id: UUID,
    limit: int = Query(default=20, ge=1, le=100),
    session: DbSession = None,
    current_user: CurrentActiveUser = None,
) -> Dict:
    """Get edit operation history."""
    # Verify project access
    project_query = select(VideoProject).where(
        VideoProject.id == project_id,
        VideoProject.user_id == current_user.id
    )
    project_result = await session.exec(project_query)
    project = project_result.first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Video project not found")
    
    # Get edit operations
    edit_ops = timeline_service.get_edit_operations(str(project_id))
    if not edit_ops:
        raise HTTPException(status_code=404, detail="Timeline not found")
    
    # Get recent history
    recent_history = edit_ops.edit_history[-limit:] if edit_ops.edit_history else []
    
    history_data = []
    for operation in recent_history:
        history_data.append({
            "id": operation.id,
            "type": operation.operation_type,
            "clip_id": operation.clip_id,
            "affected_clips": operation.affected_clips,
            "timestamp": operation.timestamp.isoformat()
        })
    
    return {
        "project_id": str(project_id),
        "history": history_data,
        "current_index": edit_ops.history_index,
        "can_undo": edit_ops.history_index >= 0,
        "can_redo": edit_ops.history_index < len(edit_ops.edit_history) - 1
    }