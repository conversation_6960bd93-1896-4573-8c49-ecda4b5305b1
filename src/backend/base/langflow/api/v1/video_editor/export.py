"""
Video Export API Endpoints
Provides professional video export capabilities with hardware acceleration and platform integration.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, status
from pydantic import BaseModel, Field

from langflow.api.utils import get_current_active_user
from langflow.services.auth.utils import verify_workspace_access
from langflow.services.export.export_service import professional_export_service, ExportError
from langflow.logging.logger import logger

router = APIRouter(prefix="/export", tags=["video_export"])


class ExportSettings(BaseModel):
    """Export configuration settings."""
    preset: Optional[str] = None
    container: str = "mp4"
    video_codec: str = "libx264"
    audio_codec: str = "aac"
    resolution: str = "1920x1080"
    fps: int = 30
    video_bitrate: Optional[str] = None
    audio_bitrate: Optional[str] = None
    quality: Optional[str] = None
    two_pass: bool = False
    gpu_encode: bool = True
    
    # Platform upload settings
    upload_platform: Optional[str] = None
    upload_metadata: Dict[str, Any] = Field(default_factory=dict)
    platform_credentials: Dict[str, str] = Field(default_factory=dict)


class ExportRequest(BaseModel):
    """Request to start video export."""
    project_id: str
    timeline_data: Dict[str, Any]
    export_settings: ExportSettings
    output_filename: str
    priority: int = Field(default=5, ge=1, le=10)


class ExportResponse(BaseModel):
    """Response from export request."""
    export_id: str
    status: str
    message: str
    estimated_duration: Optional[int] = None


class ExportStatus(BaseModel):
    """Export job status information."""
    export_id: str
    status: str
    progress: float = 0.0
    message: str = ""
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    output_path: Optional[str] = None
    file_size: Optional[int] = None
    upload_url: Optional[str] = None


class PresetInfo(BaseModel):
    """Export preset information."""
    name: str
    description: str
    container: str
    video_codec: str
    audio_codec: str
    resolution: str
    fps: int
    video_bitrate: Optional[str] = None
    audio_bitrate: Optional[str] = None
    platform_specs: Optional[Dict[str, Any]] = None


class HardwareCapabilities(BaseModel):
    """Hardware acceleration capabilities."""
    nvidia: bool
    intel: bool
    amd: bool
    codecs: Dict[str, Dict[str, str]]


@router.post("/start", response_model=ExportResponse)
async def start_export(
    request: ExportRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_active_user)
) -> ExportResponse:
    """Start a new video export job."""
    try:
        # Verify user has access to the project
        # Note: In a real implementation, you'd verify project ownership
        logger.info(f"Starting export for project {request.project_id} by user {current_user.id}")
        
        # Add export job to queue
        export_id = professional_export_service.add_export_to_queue(
            timeline_data=request.timeline_data,
            export_settings=request.export_settings.dict(),
            output_path=f"/tmp/exports/{request.output_filename}",
            priority=request.priority
        )
        
        # Start background processing
        background_tasks.add_task(
            process_export_job,
            export_id,
            request.timeline_data,
            request.export_settings.dict(),
            f"/tmp/exports/{request.output_filename}",
            current_user.id
        )
        
        return ExportResponse(
            export_id=export_id,
            status="queued",
            message="Export job has been queued for processing",
            estimated_duration=estimate_export_duration(request.timeline_data, request.export_settings)
        )
        
    except Exception as e:
        logger.error(f"Failed to start export: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start export: {str(e)}"
        )


@router.get("/status/{export_id}", response_model=ExportStatus)
async def get_export_status(
    export_id: str,
    current_user=Depends(get_current_active_user)
) -> ExportStatus:
    """Get the status of an export job."""
    try:
        # In a real implementation, you'd fetch from database
        # For now, return mock status
        return ExportStatus(
            export_id=export_id,
            status="processing",
            progress=45.0,
            message="Encoding video with hardware acceleration...",
            created_at=datetime.now(timezone.utc)
        )
        
    except Exception as e:
        logger.error(f"Failed to get export status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get export status: {str(e)}"
        )


@router.get("/queue", response_model=List[ExportStatus])
async def get_export_queue(
    current_user=Depends(get_current_active_user)
) -> List[ExportStatus]:
    """Get the current export queue status."""
    try:
        # Return queue status from export service
        queue_data = professional_export_service.export_queue.queue
        active_exports = professional_export_service.export_queue.active_exports
        
        statuses = []
        
        # Add queued jobs
        for job in queue_data:
            statuses.append(ExportStatus(
                export_id=job["id"],
                status=job["status"],
                message=f"Queued (priority: {job.get('priority', 5)})",
                created_at=datetime.fromisoformat(job["created_at"])
            ))
        
        # Add active jobs
        for job_id, job in active_exports.items():
            statuses.append(ExportStatus(
                export_id=job_id,
                status=job["status"],
                progress=job.get("progress", 0.0),
                message=job.get("message", "Processing..."),
                created_at=datetime.fromisoformat(job["created_at"]),
                started_at=datetime.fromisoformat(job.get("started_at", job["created_at"]))
            ))
        
        return statuses
        
    except Exception as e:
        logger.error(f"Failed to get export queue: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get export queue: {str(e)}"
        )


@router.delete("/cancel/{export_id}")
async def cancel_export(
    export_id: str,
    current_user=Depends(get_current_active_user)
) -> Dict[str, str]:
    """Cancel an export job."""
    try:
        # Cancel the export job
        # In a real implementation, you'd cancel the background task
        logger.info(f"Cancelling export {export_id} for user {current_user.id}")
        
        return {"message": f"Export {export_id} has been cancelled"}
        
    except Exception as e:
        logger.error(f"Failed to cancel export: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel export: {str(e)}"
        )


@router.get("/presets", response_model=Dict[str, PresetInfo])
async def get_export_presets() -> Dict[str, PresetInfo]:
    """Get available export presets."""
    try:
        presets = professional_export_service.get_supported_presets()
        
        preset_info = {}
        for preset_id, preset_data in presets.items():
            preset_info[preset_id] = PresetInfo(
                name=preset_data["name"],
                description=preset_data["description"],
                container=preset_data["container"],
                video_codec=preset_data["video_codec"],
                audio_codec=preset_data["audio_codec"],
                resolution=preset_data["resolution"],
                fps=preset_data["fps"],
                video_bitrate=preset_data.get("video_bitrate"),
                audio_bitrate=preset_data.get("audio_bitrate"),
                platform_specs=preset_data.get("platform_specs")
            )
        
        return preset_info
        
    except Exception as e:
        logger.error(f"Failed to get export presets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get export presets: {str(e)}"
        )


@router.get("/hardware", response_model=HardwareCapabilities)
async def get_hardware_capabilities() -> HardwareCapabilities:
    """Get hardware acceleration capabilities."""
    try:
        capabilities = professional_export_service.get_hardware_capabilities()
        
        return HardwareCapabilities(
            nvidia=capabilities["nvidia"],
            intel=capabilities["intel"],
            amd=capabilities["amd"],
            codecs=capabilities["codecs"]
        )
        
    except Exception as e:
        logger.error(f"Failed to get hardware capabilities: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get hardware capabilities: {str(e)}"
        )


@router.post("/upload/{export_id}")
async def upload_to_platform(
    export_id: str,
    platform: str,
    metadata: Dict[str, Any],
    credentials: Dict[str, str],
    current_user=Depends(get_current_active_user)
) -> Dict[str, Any]:
    """Upload exported video to social media platform."""
    try:
        # In a real implementation, you'd fetch the export result from database
        output_path = f"/tmp/exports/{export_id}.mp4"
        
        upload_result = await professional_export_service.platform_uploader.upload_to_platform(
            video_path=output_path,
            platform=platform,
            metadata=metadata,
            credentials=credentials
        )
        
        logger.info(f"Successfully uploaded {export_id} to {platform}")
        return upload_result
        
    except Exception as e:
        logger.error(f"Failed to upload to platform: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload to platform: {str(e)}"
        )


# Background task functions

async def process_export_job(
    export_id: str,
    timeline_data: Dict[str, Any],
    export_settings: Dict[str, Any],
    output_path: str,
    user_id: str
) -> None:
    """Process export job in background."""
    try:
        logger.info(f"Processing export job {export_id}")
        
        # Create progress callback
        async def progress_callback(progress_data: Dict[str, Any]) -> None:
            logger.info(f"Export {export_id} progress: {progress_data}")
            # In a real implementation, you'd update database or send websocket updates
        
        # Process the export
        result = await professional_export_service.export_video(
            timeline_data=timeline_data,
            export_settings=export_settings,
            output_path=output_path,
            progress_callback=progress_callback
        )
        
        # Mark job as completed
        professional_export_service.export_queue.complete_job(export_id, result)
        
        logger.info(f"Export job {export_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Export job {export_id} failed: {e}")
        professional_export_service.export_queue.fail_job(export_id, str(e))


def estimate_export_duration(timeline_data: Dict[str, Any], export_settings: ExportSettings) -> int:
    """Estimate export duration in seconds."""
    # Basic estimation based on timeline duration and settings
    timeline_duration = timeline_data.get("duration", 60)  # Default 60 seconds
    
    # Factors that affect encoding speed
    resolution_factor = 1.0
    if "4K" in export_settings.resolution or "3840" in export_settings.resolution:
        resolution_factor = 3.0
    elif "1080" in export_settings.resolution:
        resolution_factor = 1.5
    
    gpu_factor = 0.3 if export_settings.gpu_encode else 1.0
    quality_factor = 2.0 if export_settings.two_pass else 1.0
    
    # Rough estimation: timeline_duration * factors
    estimated_seconds = int(timeline_duration * resolution_factor * gpu_factor * quality_factor)
    
    return max(10, estimated_seconds)  # Minimum 10 seconds