"""Service dependencies for Lang<PERSON>."""

from typing import Optional, Type, TypeVar, Union
from uuid import UUID
from fastapi import Depends, HTTPException
from loguru import logger

from langflow.services.settings import settings_service
from langflow.services.firebase_auth.service import FirebaseAuthService
from langflow.services.schema import ServiceType

T = TypeVar("T")

_services = {}

def register_service(name: str, service: T) -> None:
    """Register a service instance."""
    _services[name] = service

def get_service(name) -> Optional[T]:
    """Get a service instance by name or ServiceType."""
    from langflow.services.manager import service_manager
    from langflow.services.schema import ServiceType

    # If it's a ServiceType enum, get from service manager
    if isinstance(name, ServiceType):
        return service_manager.services.get(name)

    # Otherwise, check the legacy _services dict for string names
    return _services.get(name)

def init_firebase_auth_service() -> Optional[FirebaseAuthService]:
    """Initialize Firebase Auth service through service manager."""
    try:
        from langflow.services.manager import service_manager
        from langflow.services.firebase_auth.factory import FirebaseAuthServiceFactory
        return service_manager.get(ServiceType.FIREBASE_AUTH_SERVICE, FirebaseAuthServiceFactory())
    except ImportError:
        # If service manager is not available, return None
        return None
    except Exception as e:
        # Log other exceptions and return None
        logger.error(f"Error initializing Firebase Auth service: {e}")
        return None

def get_firebase_auth_service() -> Optional[FirebaseAuthService]:
    """Get Firebase Auth service instance from service manager."""
    try:
        from langflow.services.manager import service_manager
        return service_manager.get(ServiceType.FIREBASE_AUTH_SERVICE)
    except ImportError:
        # If service manager is not available, return None
        return None
    except Exception as e:
        # Log other exceptions and return None
        logger.error(f"Error getting Firebase Auth service: {e}")
        return None

def get_settings_service():
    """Get settings service instance."""
    return settings_service


async def get_current_user():
    """Get current user from authentication context.

    This is a placeholder function that would normally extract the current user
    from the authentication context (JWT token, session, etc.).

    Returns:
        User object or user ID

    Raises:
        HTTPException: If user is not authenticated
    """
    # TODO: Implement actual user authentication
    # This would typically involve:
    # 1. Extracting JWT token from request headers
    # 2. Validating the token
    # 3. Extracting user information from the token
    # 4. Returning the user object

    # For now, return a mock user for development
    from langflow.services.database.models.user.model import User

    mock_user = User(
        id="dev-user-123",
        username="dev_user",
        email="<EMAIL>",
        is_active=True,
        is_superuser=False
    )

    logger.debug(f"Current user: {mock_user.username}")
    return mock_user


async def verify_workspace_access(workspace_id: str, user_id: str) -> bool:
    """Verify that a user has access to a workspace.

    Args:
        workspace_id: Workspace ID to check access for
        user_id: User ID to check access for

    Returns:
        True if user has access, raises HTTPException otherwise

    Raises:
        HTTPException: If user doesn't have access to workspace
    """
    try:
        # For now, implement a basic check
        # In a full implementation, this would check workspace membership
        # and user permissions from the database

        # Basic validation
        if not workspace_id or not user_id:
            raise HTTPException(
                status_code=400,
                detail="Missing workspace_id or user_id"
            )

        # TODO: Implement actual workspace access verification
        # This would typically involve:
        # 1. Checking if the workspace exists
        # 2. Checking if the user is a member of the workspace
        # 3. Checking the user's role/permissions in the workspace

        # For now, allow all access (development mode)
        logger.debug(f"Workspace access verified for user {user_id} in workspace {workspace_id}")
        return True

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying workspace access: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error verifying workspace access"
        )


# Custom exceptions for UUID conversion
class UUIDConversionError(Exception):
    """Raised when UUID conversion fails."""
    def __init__(self, message: str, uuid_value: str = None, context: dict = None):
        self.message = message
        self.uuid_value = uuid_value
        self.context = context or {}
        super().__init__(self.message)


class UUIDConverter:
    """Centralized UUID conversion and validation for Firebase/SQL hybrid operations."""

    @staticmethod
    def convert_firebase_uuid(uuid_value: Union[str, UUID]) -> UUID:
        """Convert Firebase string UUID to UUID object with validation.

        Args:
            uuid_value: UUID as string or UUID object

        Returns:
            UUID object

        Raises:
            UUIDConversionError: If conversion fails
        """
        if isinstance(uuid_value, UUID):
            return uuid_value

        if isinstance(uuid_value, str):
            try:
                return UUID(uuid_value)
            except ValueError as e:
                raise UUIDConversionError(
                    f"Invalid UUID format: {uuid_value}",
                    uuid_value=uuid_value
                ) from e

        raise UUIDConversionError(
            f"Unsupported UUID type: {type(uuid_value)}",
            uuid_value=str(uuid_value) if uuid_value is not None else None
        )

    @staticmethod
    def convert_query_parameters(params: dict) -> dict:
        """Convert all UUID parameters in a dictionary to proper UUID objects.

        Args:
            params: Dictionary of parameters that may contain UUID strings

        Returns:
            Dictionary with UUID strings converted to UUID objects
        """
        if not params:
            return {}

        converted_params = {}
        uuid_field_patterns = ['_id', 'workspace_id', 'user_id', 'owner_id', 'created_by', 'updated_by']

        for key, value in params.items():
            # Check if this field should be converted to UUID
            should_convert = any(pattern in key for pattern in uuid_field_patterns)

            if should_convert and value is not None:
                try:
                    converted_params[key] = UUIDConverter.convert_firebase_uuid(value)
                except UUIDConversionError as e:
                    logger.error(f"Failed to convert UUID parameter {key}={value}: {e}")
                    # Re-raise with parameter context
                    raise UUIDConversionError(
                        f"Invalid UUID in parameter '{key}': {e.message}",
                        uuid_value=str(value),
                        context={'parameter_name': key, 'original_error': str(e)}
                    ) from e
            else:
                converted_params[key] = value

        return converted_params


class HybridSessionWrapper:
    """Hybrid wrapper class that provides both Firebase Auth AND SQL database operations."""

    def __init__(self, firebase_provider, sql_engine=None):
        self.firebase_provider = firebase_provider
        self.sql_engine = sql_engine
        self._sql_session = None
        self.uuid_converter = UUIDConverter()

    def __bool__(self):
        """Return True so session checks pass."""
        return True

    @property
    def sql_session(self):
        """Get or create SQL session for database operations."""
        if self._sql_session is None and self.sql_engine:
            from sqlmodel.ext.asyncio.session import AsyncSession
            self._sql_session = AsyncSession(self.sql_engine, expire_on_commit=False)
        return self._sql_session

    async def exec(self, query, params: dict = None):
        """Execute SQL query using the SQL database engine with automatic UUID conversion.

        Args:
            query: SQL query to execute
            params: Optional parameters for the query (will be UUID-converted)

        Returns:
            Query result

        Raises:
            UUIDConversionError: If UUID conversion fails
            RuntimeError: If SQL session is not available
        """
        if not self.sql_session:
            raise RuntimeError(
                "SQL operations not available. SQL database engine not configured. "
                "Set LANGFLOW_SQL_DATABASE_URL environment variable."
            )

        # Convert UUID parameters if provided
        try:
            if params:
                converted_params = self.uuid_converter.convert_query_parameters(params)
                logger.debug(f"Converted UUID parameters: {list(converted_params.keys())}")
                result = await self.sql_session.exec(query, converted_params)
            else:
                result = await self.sql_session.exec(query)
            return result

        except UUIDConversionError as e:
            logger.error(f"UUID conversion error in fitness module: {e}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid UUID format in request: {e.message}"
            ) from e

        except Exception as e:
            # Check if this is a UUID-related error and provide helpful debugging
            error_str = str(e).lower()
            if "'str' object has no attribute 'hex'" in error_str:
                logger.error(
                    f"UUID conversion error in fitness module query. "
                    f"This indicates a Firebase string UUID was not properly converted to UUID object. "
                    f"Query: {query}, Params: {params}, Original error: {e}"
                )
                # Re-raise with more context
                raise UUIDConversionError(
                    f"Firebase/SQL UUID compatibility error: {e}. "
                    f"Check that all UUID fields are properly converted from Firebase strings.",
                    context={'query': str(query), 'params': params}
                ) from e
            else:
                # Re-raise other exceptions as-is
                raise

    async def execute(self, query, params: dict = None):
        """Execute SQL query using the SQL database engine with automatic UUID conversion.

        Note: This method is deprecated in favor of exec(). It's maintained for compatibility.

        Args:
            query: SQL query to execute
            params: Optional parameters for the query (will be UUID-converted)

        Returns:
            Query result

        Raises:
            UUIDConversionError: If UUID conversion fails
            RuntimeError: If SQL session is not available
        """
        if not self.sql_session:
            raise RuntimeError(
                "SQL operations not available. SQL database engine not configured. "
                "Set LANGFLOW_SQL_DATABASE_URL environment variable."
            )

        # Convert UUID parameters if provided
        try:
            if params:
                converted_params = self.uuid_converter.convert_query_parameters(params)
                logger.debug(f"Converted UUID parameters for execute(): {list(converted_params.keys())}")
                result = await self.sql_session.execute(query, converted_params)
            else:
                result = await self.sql_session.execute(query)
            return result

        except UUIDConversionError as e:
            logger.error(f"UUID conversion error in fitness module execute(): {e}")
            raise HTTPException(
                status_code=400,
                detail=f"Invalid UUID format in request: {e.message}"
            ) from e

        except Exception as e:
            # Check if this is a UUID-related error and provide helpful debugging
            error_str = str(e).lower()
            if "'str' object has no attribute 'hex'" in error_str:
                logger.error(
                    f"UUID conversion error in fitness module execute() query. "
                    f"This indicates a Firebase string UUID was not properly converted to UUID object. "
                    f"Query: {query}, Params: {params}, Original error: {e}"
                )
                # Re-raise with more context
                raise UUIDConversionError(
                    f"Firebase/SQL UUID compatibility error: {e}. "
                    f"Check that all UUID fields are properly converted from Firebase strings.",
                    context={'query': str(query), 'params': params}
                ) from e
            else:
                # Re-raise other exceptions as-is
                raise

    def add(self, instance):
        """Add instance to SQL session."""
        if not self.sql_session:
            raise RuntimeError("SQL operations not available.")
        return self.sql_session.add(instance)

    async def commit(self):
        """Commit SQL session."""
        if not self.sql_session:
            raise RuntimeError("SQL operations not available.")
        return await self.sql_session.commit()

    async def rollback(self):
        """Rollback SQL session."""
        if not self.sql_session:
            raise RuntimeError("SQL operations not available.")
        return await self.sql_session.rollback()

    async def refresh(self, instance):
        """Refresh instance from SQL session."""
        if not self.sql_session:
            raise RuntimeError("SQL operations not available.")
        return await self.sql_session.refresh(instance)

    async def close(self):
        """Close SQL session."""
        if self._sql_session:
            await self._sql_session.close()

    # Firebase Auth methods (for authentication operations)
    @property 
    def provider(self):
        """Access to Firebase provider for authentication."""
        return self.firebase_provider


class FirebaseSessionWrapper:
    """Wrapper class to provide Firebase provider access for API endpoints."""

    def __init__(self, provider):
        self.provider = provider

    def __bool__(self):
        """Return True so session checks pass."""
        return True

    async def exec(self, query):
        """Compatibility method for SQLModel-style exec calls.

        Firebase doesn't use SQL queries, so this method raises NotImplementedError
        to indicate that SQL operations are not supported.
        """
        raise NotImplementedError(
            "Firebase doesn't support SQL exec operations. "
            "Use Firebase-specific methods instead."
        )

    async def execute(self, query):
        """Compatibility method for SQLAlchemy-style execute calls.

        Firebase doesn't use SQL queries, so this method raises NotImplementedError
        to indicate that SQL operations are not supported.
        """
        raise NotImplementedError(
            "Firebase doesn't support SQL execute operations. "
            "Use Firebase-specific methods instead."
        )

def get_sql_engine():
    """Get or create SQL database engine for hybrid sessions."""
    import os
    
    # Check for SQL database URL in environment
    sql_database_url = os.getenv("LANGFLOW_SQL_DATABASE_URL")
    
    if not sql_database_url:
        # Default to SQLite for development if no SQL database URL provided
        sql_database_url = "sqlite+aiosqlite:///./langflow_fitness.db"
    
    try:
        from sqlalchemy.ext.asyncio import create_async_engine
        return create_async_engine(sql_database_url, echo=False)
    except Exception as e:
        logger.error(f"Error creating SQL engine: {e}")
        return None


def get_session():
    """Get database session."""
    db_service = get_db_service()
    if not db_service:
        raise RuntimeError("Database service not initialized")

    # For Firebase, return a hybrid wrapper that provides both Firebase Auth AND SQL operations
    if hasattr(db_service, 'provider') and db_service.provider and db_service.provider.provider_name == "firebase":
        sql_engine = get_sql_engine()
        return HybridSessionWrapper(db_service.provider, sql_engine)

    # For SQL-based providers, create a session
    if hasattr(db_service, 'engine') and db_service.engine:
        from sqlmodel.ext.asyncio.session import AsyncSession
        return AsyncSession(db_service.engine, expire_on_commit=False)

    raise RuntimeError("No valid database engine available")


def get_firebase_fitness_service():
    """Get Firebase fitness service instance for fitness module operations."""
    db_service = get_db_service()

    if not db_service:
        raise RuntimeError("Database service not initialized")

    # Ensure we have a Firebase provider
    if not hasattr(db_service, 'provider') or not db_service.provider:
        raise RuntimeError("Firebase provider not available")

    if db_service.provider.provider_name != "firebase":
        raise RuntimeError("Firebase provider required for fitness operations")

    # Get SQL engine for fitness data operations
    sql_engine = get_sql_engine()
    if not sql_engine:
        raise RuntimeError("SQL engine required for fitness data operations")

    # Import here to avoid circular imports
    from langflow.services.database.providers.firebase_fitness_service import FirebaseFitnessService

    # Try to get Redis client for caching (optional)
    redis_client = None
    try:
        # This would be configured if Redis is available
        # redis_client = get_redis_client()
        pass
    except Exception:
        logger.info("Redis not available, using memory cache fallback")

    return FirebaseFitnessService(
        firebase_provider=db_service.provider,
        sql_engine=sql_engine,
        redis_client=redis_client
    )

# Initialize services
def init_services():
    """Initialize all services."""
    # Initialize core services
    init_database_service()
    init_cache_service()
    init_storage_service()
    init_shared_component_cache_service()

    # Initialize application services
    init_chat_service()
    init_session_service()
    init_telemetry_service()
    init_task_service()
    init_tracing_service()
    init_socket_service()
    init_variable_service()
    init_state_service()
    init_queue_service()
    init_store_service()
    init_ai_assistant_service()

    # Initialize auth services if enabled
    if settings_service.settings.firebase_auth_enabled:
        init_firebase_auth_service()

    # Initialize Supabase auth service (always available)
    init_supabase_auth_service()

    # Initialize SAML auth service (always available for enterprise SSO)
    init_saml_auth_service()

    # Initialize OIDC auth service (always available for enterprise SSO)
    init_oidc_auth_service()

    # Initialize user provisioning service (for SSO user management)
    init_user_provisioning_service()

    # Initialize audit logging service (for compliance and security monitoring)
    init_audit_logging_service()

def init_storage_service():
    """Initialize storage service."""
    # Placeholder: Initialize and register the storage service here
    # For example, create an instance of FirebaseStorageService or other storage service
    from langflow.services.storage.factory import StorageServiceFactory
    storage_service = StorageServiceFactory().create()
    register_service("storage_service", storage_service)
    return storage_service

def get_storage_service():
    """Get storage service instance."""
    return get_service("storage_service")

def init_database_service():
    """Initialize database service."""
    from langflow.services.database.enhanced_service import EnhancedDatabaseService
    database_service = EnhancedDatabaseService(settings_service)
    register_service("database_service", database_service)
    return database_service

def get_db_service():
    """Get database service instance."""
    return get_service("database_service")

def session_scope():
    """Get database session scope."""
    db_service = get_db_service()
    if not db_service:
        raise RuntimeError("Database service not initialized")
    return db_service.with_session()

def init_cache_service():
    """Initialize cache service."""
    from langflow.services.cache.factory import CacheServiceFactory
    cache_service = CacheServiceFactory().create()
    register_service("cache_service", cache_service)
    return cache_service

def get_cache_service():
    """Get cache service instance."""
    return get_service("cache_service")

def init_chat_service():
    """Initialize chat service."""
    from langflow.services.chat.service import ChatService
    chat_service = ChatService()
    register_service("chat_service", chat_service)
    return chat_service

def get_chat_service():
    """Get chat service instance."""
    return get_service("chat_service")

def init_session_service():
    """Initialize session service."""
    from langflow.services.session.service import SessionService
    session_service = SessionService()
    register_service("session_service", session_service)
    return session_service

def get_session_service():
    """Get session service instance."""
    return get_service("session_service")

def init_telemetry_service():
    """Initialize telemetry service."""
    from langflow.services.telemetry.service import TelemetryService
    telemetry_service = TelemetryService()
    register_service("telemetry_service", telemetry_service)
    return telemetry_service

def get_telemetry_service():
    """Get telemetry service instance."""
    return get_service("telemetry_service")

def init_task_service():
    """Initialize task service."""
    from langflow.services.task.service import TaskService
    task_service = TaskService()
    register_service("task_service", task_service)
    return task_service

def get_task_service():
    """Get task service instance."""
    return get_service("task_service")

def init_tracing_service():
    """Initialize tracing service."""
    from langflow.services.tracing.service import TracingService
    tracing_service = TracingService()
    register_service("tracing_service", tracing_service)
    return tracing_service

def get_tracing_service():
    """Get tracing service instance."""
    return get_service("tracing_service")

def init_socket_service():
    """Initialize socket service."""
    from langflow.services.socket.service import SocketIOService
    socket_service = SocketIOService()
    register_service("socket_service", socket_service)
    return socket_service

def get_socket_service():
    """Get socket service instance."""
    return get_service("socket_service")

def init_variable_service():
    """Initialize variable service."""
    from langflow.services.variable.service import VariableService
    variable_service = VariableService()
    register_service("variable_service", variable_service)
    return variable_service

def get_variable_service():
    """Get variable service instance."""
    return get_service("variable_service")

def init_state_service():
    """Initialize state service."""
    from langflow.services.state.service import StateService
    state_service = StateService()
    register_service("state_service", state_service)
    return state_service

def get_state_service():
    """Get state service instance."""
    return get_service("state_service")

def init_queue_service():
    """Initialize queue service."""
    from langflow.services.job_queue.service import JobQueueService
    queue_service = JobQueueService()
    register_service("queue_service", queue_service)
    return queue_service

def get_queue_service():
    """Get queue service instance."""
    return get_service("queue_service")

def init_supabase_auth_service():
    """Initialize Supabase auth service."""
    from langflow.services.supabase_auth.service import SupabaseAuthService
    supabase_auth_service = SupabaseAuthService()
    register_service("supabase_auth_service", supabase_auth_service)
    return supabase_auth_service

def get_supabase_auth_service():
    """Get Supabase auth service instance."""
    return get_service("supabase_auth_service")

def init_saml_auth_service():
    """Initialize SAML auth service."""
    from langflow.services.saml_auth.service import SAMLAuthService
    saml_auth_service = SAMLAuthService()
    register_service("saml_auth_service", saml_auth_service)
    return saml_auth_service

def get_saml_auth_service():
    """Get SAML auth service instance."""
    return get_service("saml_auth_service")

def init_oidc_auth_service():
    """Initialize OIDC auth service."""
    from langflow.services.oidc_auth.service import OIDCAuthService
    oidc_auth_service = OIDCAuthService()
    register_service("oidc_auth_service", oidc_auth_service)
    return oidc_auth_service

def get_oidc_auth_service():
    """Get OIDC auth service instance."""
    return get_service("oidc_auth_service")

def init_user_provisioning_service():
    """Initialize user provisioning service."""
    from langflow.services.user_provisioning.service import UserProvisioningService
    user_provisioning_service = UserProvisioningService()
    register_service("user_provisioning_service", user_provisioning_service)
    return user_provisioning_service

def get_user_provisioning_service():
    """Get user provisioning service instance."""
    return get_service("user_provisioning_service")

def init_store_service():
    """Initialize store service."""
    from langflow.services.store.service import StoreService
    store_service = StoreService(settings_service)
    register_service("store_service", store_service)
    return store_service

def get_store_service():
    """Get store service instance."""
    return get_service("store_service")

def init_ai_assistant_service():
    """Initialize AI Assistant service."""
    from langflow.services.ai_assistant.service import AIAssistantService
    ai_assistant_service = AIAssistantService(settings_service)
    register_service("ai_assistant_service", ai_assistant_service)
    return ai_assistant_service

def get_ai_assistant_service():
    """Get AI Assistant service instance."""
    return get_service("ai_assistant_service")

def init_shared_component_cache_service():
    """Initialize shared component cache service."""
    from langflow.services.cache.service import SharedComponentCacheService
    shared_component_cache_service = SharedComponentCacheService()
    register_service("shared_component_cache_service", shared_component_cache_service)
    return shared_component_cache_service

def get_shared_component_cache_service():
    """Get shared component cache service instance."""
    return get_service("shared_component_cache_service")

def init_audit_logging_service():
    """Initialize audit logging service."""
    from langflow.services.audit_logging.service import AuditLoggingService
    audit_logging_service = AuditLoggingService()
    register_service("audit_logging_service", audit_logging_service)
    return audit_logging_service

def get_audit_logging_service():
    """Get audit logging service instance."""
    return get_service("audit_logging_service")
