from datetime import datetime, timezone
from typing import TYPE_CHECKING, Dict, Any
from uuid import uuid4

from sqlalchemy import Text, Column, JSON
from sqlmodel import Field, Relationship, SQLModel

from langflow.schema.serialize import UUIDstr
from langflow.services.database.models.utils import create_uuid_primary_key

if TYPE_CHECKING:
    from langflow.services.database.models.user import User
    from langflow.services.database.models.social_media.post import SocialPost


class PostCommentBase(SQLModel):
    """Base model for Post Comment."""
    content: str = Field(sa_column=Column(Text))
    comment_type: str = Field(default="internal", index=True)  # internal, approval, feedback
    is_resolved: bool = Field(default=False, index=True)

    # Position data for visual comments
    position_x: float | None = Field(default=None)
    position_y: float | None = Field(default=None)

    # Metadata
    comment_metadata: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))


class PostComment(PostCommentBase, table=True):  # type: ignore[call-arg]
    """Post Comment model for database."""
    __tablename__ = "post_comment"

    id: UUIDstr = create_uuid_primary_key()
    post_id: UUIDstr = Field(index=True, foreign_key="social_post.id")
    parent_comment_id: UUIDstr | None = Field(default=None, foreign_key="post_comment.id")
    created_by: UUIDstr = Field(index=True, foreign_key="user.id")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    post: "SocialPost" = Relationship(back_populates="comments")
    creator: "User" = Relationship(back_populates="created_post_comments", sa_relationship_kwargs={"foreign_keys": "PostComment.created_by"})
    parent_comment: "PostComment" = Relationship(back_populates="replies", sa_relationship_kwargs={"remote_side": "PostComment.id"})
    replies: list["PostComment"] = Relationship(back_populates="parent_comment")


class PostCommentCreate(PostCommentBase):
    """Model for creating a new post comment."""
    post_id: UUIDstr
    parent_comment_id: UUIDstr | None = None


class PostCommentRead(PostCommentBase):
    """Model for reading a post comment."""
    id: UUIDstr
    post_id: UUIDstr
    parent_comment_id: UUIDstr | None
    created_by: UUIDstr
    created_at: datetime
    updated_at: datetime


class PostCommentUpdate(SQLModel):
    """Model for updating a post comment."""
    content: str | None = None
    is_resolved: bool | None = None
    position_x: float | None = None
    position_y: float | None = None
    comment_metadata: Dict[str, Any] | None = None
