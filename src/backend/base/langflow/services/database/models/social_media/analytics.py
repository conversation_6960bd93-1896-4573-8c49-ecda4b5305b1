from datetime import datetime, timezone
from typing import TYPE_CHECKING, Dict, Any, Optional
from uuid import UUID, uuid4

from sqlalchemy import Text, Column, JSON
from sqlmodel import Field, Relationship, SQLModel

from langflow.schema.serialize import UUIDstr
from langflow.services.database.models.utils import create_uuid_primary_key

if TYPE_CHECKING:
    from langflow.services.database.models.social_media.post import SocialPost


class PostAnalyticsBase(SQLModel):
    """Base model for Post Analytics."""
    platform: str = Field(index=True)

    # Engagement metrics
    likes: int = Field(default=0, index=True)
    comments: int = Field(default=0, index=True)
    shares: int = Field(default=0, index=True)
    saves: int = Field(default=0, index=True)
    clicks: int = Field(default=0, index=True)
    impressions: int = Field(default=0, index=True)
    reach: int = Field(default=0, index=True)

    # Video-specific metrics
    video_views: int = Field(default=0, index=True)
    video_completion_rate: float = Field(default=0.0)
    average_watch_time: float = Field(default=0.0)  # In seconds

    # Platform-specific metrics
    platform_metrics: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))

    # Demographic data
    audience_demographics: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))

    # Performance scores
    engagement_rate: float = Field(default=0.0, index=True)
    virality_score: float = Field(default=0.0, index=True)

    # Timestamps
    collected_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), index=True)
    data_date: datetime = Field(index=True)  # The date this data represents


class PostAnalytics(PostAnalyticsBase, table=True):  # type: ignore[call-arg]
    """Post Analytics model for database."""
    __tablename__ = "post_analytics"

    id: UUIDstr = create_uuid_primary_key()
    post_id: UUIDstr = Field(index=True, foreign_key="social_post.id")

    # Relationships
    post: "SocialPost" = Relationship(back_populates="analytics")


class PostAnalyticsCreate(PostAnalyticsBase):
    """Model for creating new post analytics."""
    post_id: UUIDstr
    data_date: datetime


class PostAnalyticsRead(PostAnalyticsBase):
    """Model for reading post analytics."""
    id: UUIDstr
    post_id: UUIDstr


class PostAnalyticsUpdate(SQLModel):
    """Model for updating post analytics."""
    likes: int | None = None
    comments: int | None = None
    shares: int | None = None
    saves: int | None = None
    clicks: int | None = None
    impressions: int | None = None
    reach: int | None = None
    video_views: int | None = None
    video_completion_rate: float | None = None
    average_watch_time: float | None = None
    platform_metrics: dict | None = None
    audience_demographics: dict | None = None
    engagement_rate: float | None = None
    virality_score: float | None = None
