from datetime import datetime, timezone
from typing import TYPE_CHECKING, Optional, Dict, Any, List
from uuid import UUID, uuid4

from sqlalchemy import Text, Column, JSON
from sqlmodel import Field, Relationship, SQLModel

from langflow.schema.serialize import UUIDstr
from langflow.services.database.models.utils import create_uuid_primary_key

if TYPE_CHECKING:
    from langflow.services.database.models.user import User
    from langflow.services.database.models.workspace import Workspace
    from langflow.services.database.models.social_media.account import SocialMediaAccount
    from langflow.services.database.models.social_media.analytics import PostAnalytics
    from langflow.services.database.models.social_media.comment import PostComment


class SocialPostBase(SQLModel):
    """Base model for Social Post."""
    content: str = Field(sa_column=Column(Text))
    platform_specific_content: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))
    media_urls: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    hashtags: List[str] = Field(default_factory=list, sa_column=Column(JSON))
    mentions: list[str] = Field(default_factory=list, sa_column=Column(JSON))

    # Scheduling
    scheduled_at: datetime | None = Field(default=None, index=True)
    published_at: datetime | None = Field(default=None, index=True)
    status: str = Field(default="draft", index=True)  # draft, scheduled, published, failed, cancelled

    # Platform response data
    platform_post_id: str | None = Field(default=None, index=True)
    platform_response: dict = Field(default_factory=dict, sa_column=Column(JSON))

    # Approval workflow
    approval_status: str = Field(default="pending", index=True)  # pending, approved, rejected, auto_approved
    rejection_reason: str | None = Field(default=None, sa_column=Column(Text))

    # Post metadata
    post_type: str = Field(default="post", index=True)  # post, story, reel, video, carousel
    is_promoted: bool = Field(default=False, index=True)
    promotion_budget: float | None = Field(default=None)
    target_audience: dict = Field(default_factory=dict, sa_column=Column(JSON))


class SocialPost(SocialPostBase, table=True):  # type: ignore[call-arg]
    """Social Post model for database."""
    __tablename__ = "social_post"

    id: UUIDstr = create_uuid_primary_key()
    workspace_id: UUIDstr = Field(index=True, foreign_key="workspace.id")
    account_id: UUIDstr = Field(index=True, foreign_key="social_media_account.id")
    approved_by: UUIDstr | None = Field(default=None, foreign_key="user.id")
    approved_at: datetime | None = Field(default=None)
    created_by: UUIDstr = Field(index=True, foreign_key="user.id")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    workspace: "Workspace" = Relationship(back_populates="social_posts")
    account: "SocialMediaAccount" = Relationship(back_populates="posts")
    creator: "User" = Relationship(back_populates="created_posts", sa_relationship_kwargs={"foreign_keys": "SocialPost.created_by"})
    approver: "User" = Relationship(back_populates="approved_posts", sa_relationship_kwargs={"foreign_keys": "SocialPost.approved_by"})
    analytics: list["PostAnalytics"] = Relationship(back_populates="post", sa_relationship_kwargs={"cascade": "delete"})
    comments: list["PostComment"] = Relationship(back_populates="post", sa_relationship_kwargs={"cascade": "delete"})


class SocialPostCreate(SocialPostBase):
    """Model for creating a new social post."""
    workspace_id: UUIDstr
    account_id: UUIDstr


class SocialPostRead(SocialPostBase):
    """Model for reading a social post."""
    id: UUIDstr
    workspace_id: UUIDstr
    account_id: UUIDstr
    approved_by: UUIDstr | None
    approved_at: datetime | None
    created_by: UUIDstr
    created_at: datetime
    updated_at: datetime


class SocialPostUpdate(SQLModel):
    """Model for updating a social post."""
    content: str | None = None
    platform_specific_content: dict | None = None
    media_urls: list[str] | None = None
    hashtags: list[str] | None = None
    mentions: list[str] | None = None
    scheduled_at: datetime | None = None
    status: str | None = None
    approval_status: str | None = None
    rejection_reason: str | None = None
    post_type: str | None = None
    is_promoted: bool | None = None
    promotion_budget: float | None = None
    target_audience: dict | None = None
