from datetime import datetime, timezone
from typing import TYPE_CHECKING, Optional, Dict, Any, List
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import Text, Column, JSON
from sqlmodel import Field, Relationship, SQLModel

from langflow.schema.serialize import UUIDstr

if TYPE_CHECKING:
    from langflow.services.database.models.user import User
    from langflow.services.database.models.workspace import Workspace


class TemplateVisibility(str, Enum):
    """Template visibility enumeration."""
    PRIVATE = "private"  # Only visible to creator
    WORKSPACE = "workspace"  # Visible to workspace members
    PUBLIC = "public"  # Visible to all users


class TemplateCategory(str, Enum):
    """Template category enumeration."""
    SOFTWARE_DEVELOPMENT = "software_development"
    MARKETING = "marketing"
    DESIGN = "design"
    RESEARCH = "research"
    EDUCATION = "education"
    BUSINESS = "business"
    PERSONAL = "personal"
    AGILE = "agile"
    WATERFALL = "waterfall"
    CUSTOM = "custom"


class ProjectTemplateBase(SQLModel):
    """Base model for ProjectTemplate."""
    name: str = Field(index=True, max_length=255)
    description: str | None = Field(default=None, sa_column=Column(Text))
    category: TemplateCategory = Field(default=TemplateCategory.CUSTOM, index=True)
    tags: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    visibility: TemplateVisibility = Field(default=TemplateVisibility.PRIVATE, index=True)
    is_system: bool = Field(default=False, index=True)  # System-provided templates
    usage_count: int = Field(default=0, index=True)
    
    # Template structure
    project_structure: Dict[str, Any] = Field(sa_column=Column(JSON))  # Project configuration
    task_templates: List[Dict[str, Any]] = Field(default_factory=list, sa_column=Column(JSON))  # Task templates
    sprint_templates: List[Dict[str, Any]] = Field(default_factory=list, sa_column=Column(JSON))  # Sprint templates
    member_roles: List[Dict[str, Any]] = Field(default_factory=list, sa_column=Column(JSON))  # Default member roles


class ProjectTemplate(ProjectTemplateBase, table=True):  # type: ignore[call-arg]
    """ProjectTemplate model for database."""
    __tablename__ = "project_template"
    __table_args__ = {"extend_existing": True}

    id: UUIDstr = Field(default_factory=uuid4, primary_key=True, unique=True)
    workspace_id: UUIDstr | None = Field(default=None, index=True, foreign_key="workspace.id")
    created_by: UUIDstr = Field(index=True, foreign_key="user.id")
    updated_by: UUIDstr = Field(index=True, foreign_key="user.id")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    workspace: "Workspace" = Relationship(back_populates="project_templates")
    creator: "User" = Relationship(
        back_populates="created_project_templates",
        sa_relationship_kwargs={"foreign_keys": "ProjectTemplate.created_by"}
    )
    updater: "User" = Relationship(
        back_populates="updated_project_templates",
        sa_relationship_kwargs={"foreign_keys": "ProjectTemplate.updated_by"}
    )


class ProjectTemplateCreate(ProjectTemplateBase):
    """Model for creating a new project template."""
    workspace_id: UUIDstr | None = None


class ProjectTemplateRead(ProjectTemplateBase):
    """Model for reading a project template."""
    id: UUIDstr
    workspace_id: UUIDstr | None
    created_by: UUIDstr
    updated_by: UUIDstr
    created_at: datetime
    updated_at: datetime


class ProjectTemplateUpdate(SQLModel):
    """Model for updating a project template."""
    name: str | None = None
    description: str | None = None
    category: TemplateCategory | None = None
    tags: list[str] | None = None
    visibility: TemplateVisibility | None = None
    project_structure: dict | None = None
    task_templates: list[dict] | None = None
    sprint_templates: list[dict] | None = None
    member_roles: list[dict] | None = None


class ProjectFromTemplateCreate(SQLModel):
    """Model for creating a project from a template."""
    template_id: UUIDstr
    workspace_id: UUIDstr
    project_name: str
    project_description: str | None = None
    project_owner_id: UUIDstr
    client_id: UUIDstr | None = None
    start_date: datetime | None = None
    end_date: datetime | None = None
    budget: float | None = None
    currency: str = "USD"
    
    # Template customization options
    include_tasks: bool = True
    include_sprints: bool = True
    include_members: bool = False  # Don't auto-add members by default
    customize_dates: bool = True  # Adjust task/sprint dates based on project dates


# System template definitions for common project types
SYSTEM_TEMPLATES = [
    {
        "name": "Software Development Project",
        "description": "Standard software development project with common phases and tasks",
        "category": TemplateCategory.SOFTWARE_DEVELOPMENT,
        "tags": ["software", "development", "agile", "scrum"],
        "visibility": TemplateVisibility.PUBLIC,
        "is_system": True,
        "project_structure": {
            "priority": "medium",
            "status": "planning"
        },
        "task_templates": [
            {
                "title": "Project Setup",
                "description": "Initialize project repository and development environment",
                "priority": "high",
                "estimated_hours": 8,
                "kanban_column": "todo"
            },
            {
                "title": "Requirements Analysis",
                "description": "Gather and document project requirements",
                "priority": "high",
                "estimated_hours": 16,
                "kanban_column": "todo"
            },
            {
                "title": "System Design",
                "description": "Create system architecture and design documents",
                "priority": "high",
                "estimated_hours": 24,
                "kanban_column": "todo"
            },
            {
                "title": "Development Phase 1",
                "description": "Implement core functionality",
                "priority": "medium",
                "estimated_hours": 80,
                "kanban_column": "todo"
            },
            {
                "title": "Testing & QA",
                "description": "Comprehensive testing and quality assurance",
                "priority": "high",
                "estimated_hours": 32,
                "kanban_column": "todo"
            },
            {
                "title": "Deployment",
                "description": "Deploy application to production environment",
                "priority": "high",
                "estimated_hours": 16,
                "kanban_column": "todo"
            }
        ],
        "sprint_templates": [
            {
                "name": "Sprint 1 - Setup & Planning",
                "description": "Project initialization and planning phase",
                "goal": "Complete project setup and requirements analysis"
            },
            {
                "name": "Sprint 2 - Core Development",
                "description": "Implement core application features",
                "goal": "Deliver MVP functionality"
            },
            {
                "name": "Sprint 3 - Testing & Polish",
                "description": "Testing, bug fixes, and final preparations",
                "goal": "Prepare for production deployment"
            }
        ],
        "member_roles": [
            {"role": "owner", "description": "Project lead and decision maker"},
            {"role": "manager", "description": "Development team lead"},
            {"role": "contributor", "description": "Developer team member"}
        ]
    },
    {
        "name": "Marketing Campaign",
        "description": "Marketing campaign project with typical phases and deliverables",
        "category": TemplateCategory.MARKETING,
        "tags": ["marketing", "campaign", "promotion", "advertising"],
        "visibility": TemplateVisibility.PUBLIC,
        "is_system": True,
        "project_structure": {
            "priority": "medium",
            "status": "planning"
        },
        "task_templates": [
            {
                "title": "Campaign Strategy",
                "description": "Define campaign objectives, target audience, and key messages",
                "priority": "high",
                "estimated_hours": 16,
                "kanban_column": "todo"
            },
            {
                "title": "Content Creation",
                "description": "Create marketing materials and content",
                "priority": "medium",
                "estimated_hours": 40,
                "kanban_column": "todo"
            },
            {
                "title": "Channel Setup",
                "description": "Set up marketing channels and platforms",
                "priority": "medium",
                "estimated_hours": 12,
                "kanban_column": "todo"
            },
            {
                "title": "Campaign Launch",
                "description": "Execute campaign launch across all channels",
                "priority": "high",
                "estimated_hours": 8,
                "kanban_column": "todo"
            },
            {
                "title": "Performance Monitoring",
                "description": "Monitor campaign performance and metrics",
                "priority": "medium",
                "estimated_hours": 20,
                "kanban_column": "todo"
            },
            {
                "title": "Campaign Analysis",
                "description": "Analyze results and prepare final report",
                "priority": "medium",
                "estimated_hours": 12,
                "kanban_column": "todo"
            }
        ],
        "sprint_templates": [
            {
                "name": "Planning Phase",
                "description": "Campaign planning and strategy development",
                "goal": "Complete campaign strategy and content plan"
            },
            {
                "name": "Execution Phase",
                "description": "Campaign launch and active management",
                "goal": "Successfully launch and manage campaign"
            },
            {
                "name": "Analysis Phase",
                "description": "Performance analysis and reporting",
                "goal": "Complete campaign analysis and recommendations"
            }
        ],
        "member_roles": [
            {"role": "owner", "description": "Campaign manager"},
            {"role": "contributor", "description": "Content creator"},
            {"role": "contributor", "description": "Marketing specialist"}
        ]
    }
]
