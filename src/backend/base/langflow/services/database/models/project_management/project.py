from datetime import datetime, timezone
from typing import TYPE_CHECKING, Optional, Dict, Any
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import Text, Column, JSON
from sqlmodel import Field, Relationship, SQLModel

from langflow.schema.serialize import UUIDstr

if TYPE_CHECKING:
    from langflow.services.database.models.user import User
    from langflow.services.database.models.workspace import Workspace
    from langflow.services.database.models.crm.client import Client
    from langflow.services.database.models.project_management.task import ProjectTask
    from langflow.services.database.models.project_management.sprint import Sprint
    from langflow.services.database.models.project_management.project_member import ProjectMember


class ProjectStatus(str, Enum):
    """Project status enumeration."""
    PLANNING = "planning"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Priority(str, Enum):
    """Priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class ProjectBase(SQLModel):
    """Base model for Project."""
    name: str = Field(index=True, max_length=255)
    description: str | None = Field(default=None, sa_column=Column(Text))
    status: ProjectStatus = Field(default=ProjectStatus.PLANNING, index=True)
    priority: Priority = Field(default=Priority.MEDIUM, index=True)
    start_date: datetime | None = Field(default=None, index=True)
    end_date: datetime | None = Field(default=None, index=True)
    budget: float | None = Field(default=None)
    currency: str = Field(default="USD", max_length=3)
    tags: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    settings: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))


class Project(ProjectBase, table=True):  # type: ignore[call-arg]
    """Project model for database."""
    __tablename__ = "project"
    __table_args__ = {"extend_existing": True}

    id: UUIDstr = Field(default_factory=uuid4, primary_key=True, unique=True)
    workspace_id: UUIDstr = Field(index=True, foreign_key="workspace.id")
    project_owner_id: UUIDstr = Field(index=True, foreign_key="user.id")
    client_id: UUIDstr | None = Field(default=None, foreign_key="client.id")
    template_id: UUIDstr | None = Field(default=None)  # For future project templates
    created_by: UUIDstr = Field(index=True, foreign_key="user.id")
    updated_by: UUIDstr = Field(index=True, foreign_key="user.id")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    workspace: "Workspace" = Relationship(back_populates="projects")
    owner: "User" = Relationship(
        back_populates="owned_projects", 
        sa_relationship_kwargs={"foreign_keys": "Project.project_owner_id"}
    )
    client: "Client" = Relationship(back_populates="projects")
    creator: "User" = Relationship(
        back_populates="created_projects", 
        sa_relationship_kwargs={"foreign_keys": "Project.created_by"}
    )
    updater: "User" = Relationship(
        back_populates="updated_projects", 
        sa_relationship_kwargs={"foreign_keys": "Project.updated_by"}
    )
    tasks: list["ProjectTask"] = Relationship(
        back_populates="project",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    sprints: list["Sprint"] = Relationship(
        back_populates="project", 
        sa_relationship_kwargs={"cascade": "delete"}
    )
    members: list["ProjectMember"] = Relationship(
        back_populates="project", 
        sa_relationship_kwargs={"cascade": "delete"}
    )


class ProjectCreate(ProjectBase):
    """Model for creating a new project."""
    workspace_id: UUIDstr
    project_owner_id: UUIDstr
    client_id: UUIDstr | None = None
    template_id: UUIDstr | None = None


class ProjectRead(ProjectBase):
    """Model for reading a project."""
    id: UUIDstr
    workspace_id: UUIDstr
    project_owner_id: UUIDstr
    client_id: UUIDstr | None
    template_id: UUIDstr | None
    created_by: UUIDstr
    updated_by: UUIDstr
    created_at: datetime
    updated_at: datetime


class ProjectUpdate(SQLModel):
    """Model for updating a project."""
    name: str | None = None
    description: str | None = None
    status: ProjectStatus | None = None
    priority: Priority | None = None
    start_date: datetime | None = None
    end_date: datetime | None = None
    budget: float | None = None
    currency: str | None = None
    project_owner_id: UUIDstr | None = None
    client_id: UUIDstr | None = None
    template_id: UUIDstr | None = None
