"""CommunicationLog model for tracking all client communications."""

from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING
from uuid import uuid4

from sqlmodel import Field, Relationship, SQLModel, Column
from sqlalchemy import <PERSON><PERSON><PERSON>

from langflow.schema.serialize import UUIDstr

if TYPE_CHECKING:
    from langflow.services.database.models.user import User
    from langflow.services.database.models.workspace import Workspace
    from langflow.services.database.models.crm.client import Client
    from langflow.services.database.models.crm.company import Company


class CommunicationLogBase(SQLModel):
    """Base model for CommunicationLog."""
    contact_id: UUIDstr = Field(foreign_key="client.id", index=True)
    company_id: Optional[UUIDstr] = Field(default=None, foreign_key="company.id", index=True)
    communication_type: str = Field(index=True)  # email, call, meeting, note, social, sms, video_call
    direction: str = Field(index=True)  # inbound, outbound
    subject: str = Field(min_length=1, max_length=500)
    content: Optional[str] = Field(default=None)
    duration_minutes: Optional[int] = Field(default=None, ge=0)  # for calls/meetings
    outcome: Optional[str] = Field(default=None)  # successful, no_answer, follow_up_needed, voicemail, bounced
    follow_up_date: Optional[datetime] = Field(default=None)
    follow_up_completed: bool = Field(default=False)
    attachments: Optional[List[str]] = Field(default=None)  # file URLs or references
    metadata: Optional[dict] = Field(default=None, sa_column=Column(JSON))  # additional communication metadata
    response_time_minutes: Optional[int] = Field(default=None, ge=0)  # time to respond to incoming communication
    thread_id: Optional[str] = Field(default=None, index=True)  # for grouping related communications
    external_id: Optional[str] = Field(default=None, index=True)  # ID from external system (email, CRM, etc.)
    tags: Optional[List[str]] = Field(default=None)  # communication tags for categorization
    priority: str = Field(default="normal")  # low, normal, high, urgent
    status: str = Field(default="completed")  # draft, sent, completed, failed
    sentiment: Optional[str] = Field(default=None)  # positive, neutral, negative (from AI analysis)
    is_automated: bool = Field(default=False)  # whether this was an automated communication
    language: Optional[str] = Field(default=None)  # communication language
    channel: Optional[str] = Field(default=None)  # specific channel (gmail, outlook, slack, etc.)


class CommunicationLog(CommunicationLogBase, table=True):
    """CommunicationLog model for database."""
    __tablename__ = "communication_log"
    
    id: UUIDstr = Field(default_factory=uuid4, primary_key=True, unique=True)
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), index=True)
    user_id: UUIDstr = Field(foreign_key="user.id", index=True)
    workspace_id: UUIDstr = Field(foreign_key="workspace.id", index=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    contact: "Client" = Relationship(back_populates="communications")
    company: Optional["Company"] = Relationship(back_populates="communications")
    user: "User" = Relationship(back_populates="logged_communications")
    workspace: "Workspace" = Relationship(back_populates="communications")


class CommunicationLogCreate(CommunicationLogBase):
    """Model for creating a new communication log."""
    pass


class CommunicationLogRead(CommunicationLogBase):
    """Model for reading a communication log."""
    id: UUIDstr
    timestamp: datetime
    user_id: UUIDstr
    workspace_id: UUIDstr
    created_at: datetime
    updated_at: datetime


class CommunicationLogUpdate(SQLModel):
    """Model for updating a communication log."""
    contact_id: Optional[UUIDstr] = None
    company_id: Optional[UUIDstr] = None
    communication_type: Optional[str] = None
    direction: Optional[str] = None
    subject: Optional[str] = None
    content: Optional[str] = None
    duration_minutes: Optional[int] = None
    outcome: Optional[str] = None
    follow_up_date: Optional[datetime] = None
    follow_up_completed: Optional[bool] = None
    attachments: Optional[List[str]] = None
    metadata: Optional[dict] = None
    response_time_minutes: Optional[int] = None
    thread_id: Optional[str] = None
    external_id: Optional[str] = None
    tags: Optional[List[str]] = None
    priority: Optional[str] = None
    status: Optional[str] = None
    sentiment: Optional[str] = None
    is_automated: Optional[bool] = None
    language: Optional[str] = None
    channel: Optional[str] = None


class CommunicationTemplate(SQLModel, table=True):
    """CommunicationTemplate model for reusable communication templates."""
    __tablename__ = "communication_template"
    
    id: UUIDstr = Field(default_factory=uuid4, primary_key=True, unique=True)
    name: str = Field(min_length=1, max_length=200)
    description: Optional[str] = Field(default=None)
    template_type: str = Field(index=True)  # email, call_script, meeting_agenda, note, sms
    subject_template: Optional[str] = Field(default=None)
    content_template: str = Field(min_length=1)
    variables: List[str] = Field(default_factory=list)  # placeholders like {client_name}, {company_name}
    category: Optional[str] = Field(default=None, index=True)
    tags: Optional[List[str]] = Field(default=None)
    is_active: bool = Field(default=True)
    usage_count: int = Field(default=0, ge=0)
    last_used: Optional[datetime] = Field(default=None)
    workspace_id: UUIDstr = Field(foreign_key="workspace.id", index=True)
    created_by: UUIDstr = Field(foreign_key="user.id", index=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    workspace: "Workspace" = Relationship(back_populates="communication_templates")
    creator: "User" = Relationship(back_populates="created_communication_templates")


class CommunicationTemplateCreate(SQLModel):
    """Model for creating a new communication template."""
    name: str = Field(min_length=1, max_length=200)
    description: Optional[str] = None
    template_type: str
    subject_template: Optional[str] = None
    content_template: str = Field(min_length=1)
    variables: List[str] = Field(default_factory=list)
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    is_active: bool = Field(default=True)
    workspace_id: UUIDstr


class CommunicationTemplateRead(SQLModel):
    """Model for reading a communication template."""
    id: UUIDstr
    name: str
    description: Optional[str]
    template_type: str
    subject_template: Optional[str]
    content_template: str
    variables: List[str]
    category: Optional[str]
    tags: Optional[List[str]]
    is_active: bool
    usage_count: int
    last_used: Optional[datetime]
    workspace_id: UUIDstr
    created_by: UUIDstr
    created_at: datetime
    updated_at: datetime


class CommunicationTemplateUpdate(SQLModel):
    """Model for updating a communication template."""
    name: Optional[str] = None
    description: Optional[str] = None
    template_type: Optional[str] = None
    subject_template: Optional[str] = None
    content_template: Optional[str] = None
    variables: Optional[List[str]] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class FollowUpReminder(SQLModel, table=True):
    """FollowUpReminder model for tracking follow-up tasks."""
    __tablename__ = "follow_up_reminder"
    
    id: UUIDstr = Field(default_factory=uuid4, primary_key=True, unique=True)
    communication_id: UUIDstr = Field(foreign_key="communication_log.id", index=True)
    contact_id: UUIDstr = Field(foreign_key="client.id", index=True)
    assigned_to: UUIDstr = Field(foreign_key="user.id", index=True)
    title: str = Field(min_length=1, max_length=200)
    description: Optional[str] = Field(default=None)
    scheduled_for: datetime = Field(index=True)
    completed_at: Optional[datetime] = Field(default=None)
    status: str = Field(default="pending", index=True)  # pending, completed, cancelled, overdue
    priority: str = Field(default="normal")  # low, normal, high, urgent
    reminder_sent: bool = Field(default=False)
    snooze_until: Optional[datetime] = Field(default=None)
    workspace_id: UUIDstr = Field(foreign_key="workspace.id", index=True)
    created_by: UUIDstr = Field(foreign_key="user.id", index=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    communication: "CommunicationLog" = Relationship(back_populates="follow_ups")
    contact: "Client" = Relationship(back_populates="follow_ups")
    assignee: "User" = Relationship(back_populates="assigned_follow_ups", sa_relationship_kwargs={"foreign_keys": "[FollowUpReminder.assigned_to]"})
    creator: "User" = Relationship(back_populates="created_follow_ups", sa_relationship_kwargs={"foreign_keys": "[FollowUpReminder.created_by]"})
    workspace: "Workspace" = Relationship(back_populates="follow_up_reminders")


class FollowUpReminderCreate(SQLModel):
    """Model for creating a new follow-up reminder."""
    communication_id: UUIDstr
    contact_id: UUIDstr
    assigned_to: UUIDstr
    title: str = Field(min_length=1, max_length=200)
    description: Optional[str] = None
    scheduled_for: datetime
    priority: str = Field(default="normal")
    workspace_id: UUIDstr


class FollowUpReminderRead(SQLModel):
    """Model for reading a follow-up reminder."""
    id: UUIDstr
    communication_id: UUIDstr
    contact_id: UUIDstr
    assigned_to: UUIDstr
    title: str
    description: Optional[str]
    scheduled_for: datetime
    completed_at: Optional[datetime]
    status: str
    priority: str
    reminder_sent: bool
    snooze_until: Optional[datetime]
    workspace_id: UUIDstr
    created_by: UUIDstr
    created_at: datetime
    updated_at: datetime


class FollowUpReminderUpdate(SQLModel):
    """Model for updating a follow-up reminder."""
    assigned_to: Optional[UUIDstr] = None
    title: Optional[str] = None
    description: Optional[str] = None
    scheduled_for: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: Optional[str] = None
    priority: Optional[str] = None
    reminder_sent: Optional[bool] = None
    snooze_until: Optional[datetime] = None


class CommunicationTimelineView(SQLModel):
    """View model for communication timeline display."""
    id: UUIDstr
    contact_id: UUIDstr
    contact_name: str
    company_id: Optional[UUIDstr]
    company_name: Optional[str]
    communication_type: str
    direction: str
    subject: str
    content: Optional[str]
    outcome: Optional[str]
    timestamp: datetime
    user_name: str
    duration_minutes: Optional[int]
    follow_up_date: Optional[datetime]
    follow_up_completed: bool
    attachments: Optional[List[str]]
    tags: Optional[List[str]]
    priority: str
    status: str
    sentiment: Optional[str]


class CommunicationAnalytics(SQLModel):
    """Model for communication analytics data."""
    workspace_id: UUIDstr
    total_communications: int
    avg_response_time_minutes: Optional[float]
    communication_types: dict  # type -> count
    communication_directions: dict  # direction -> count
    outcomes: dict  # outcome -> count
    sentiment_distribution: dict  # sentiment -> count
    daily_volume: List[dict]  # date, count pairs
    top_contacts: List[dict]  # contact data with communication counts
    pending_follow_ups: int
    overdue_follow_ups: int