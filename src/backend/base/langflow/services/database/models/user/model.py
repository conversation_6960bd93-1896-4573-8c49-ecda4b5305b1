from datetime import datetime, timezone
from typing import TYPE_CHECKING, Any, List, Dict
from uuid import UUID, uuid4

from pydantic import BaseModel
from sqlalchemy import JSON, Column
from sqlmodel import Field, Relationship, SQLModel

from langflow.schema.serialize import UUIDstr

if TYPE_CHECKING:
    from langflow.services.database.models.api_key import <PERSON><PERSON><PERSON><PERSON>
    from langflow.services.database.models.flow import Flow
    from langflow.services.database.models.folder import Folder
    from langflow.services.database.models.variable import Variable
    from langflow.services.database.models.workspace import Workspace, WorkspaceMember
    from langflow.services.database.models.crm.client import Client
    from langflow.services.database.models.crm.company import Company
    from langflow.services.database.models.crm.contact_relationship import ContactRelationship
    from langflow.services.database.models.crm.communication_log import CommunicationLog, CommunicationTemplate, FollowUpReminder
    from langflow.services.database.models.crm.invoice import Invoice
    from langflow.services.database.models.crm.opportunity import Opportunity
    from langflow.services.database.models.crm.task import Task
    from langflow.services.database.models.crm.product import Product
    from langflow.services.database.models.crm.product_category import ProductCategory
    from langflow.services.database.models.video_editor.video_project import VideoProject
    from langflow.services.database.models.video_editor.video_template import VideoTemplate
    from langflow.services.database.models.crm.product_attribute import ProductAttribute
    from langflow.services.database.models.crm.product_review import ProductReview
    from langflow.services.database.models.book import (
        Book, BookTemplate, BookCollaboration, BookComment, BookSession,
        TemplateComment, TemplateRating, TemplateFeedback,
        TemplateApprovalWorkflow, TemplateApprovalRequest, TemplateApproval, TemplateApprovalNotification,
        TemplateUsage, TemplateReport
    )

    from langflow.services.database.models.social_media.account import SocialMediaAccount
    from langflow.services.database.models.social_media.post import SocialPost
    from langflow.services.database.models.social_media.content_library import ContentLibraryItem
    from langflow.services.database.models.social_media.comment import PostComment

    from langflow.services.database.models.design import (
        DesignProject, DesignTemplate, DesignAsset, DesignVersion
    )

    from langflow.services.database.models.project_management.project import Project
    from langflow.services.database.models.project_management.task import Task as ProjectTask
    from langflow.services.database.models.project_management.sprint import Sprint
    from langflow.services.database.models.project_management.project_member import ProjectMember
    from langflow.services.database.models.project_management.time_entry import TimeEntry
    from langflow.services.database.models.project_management.task_dependency import TaskDependency
    from langflow.services.database.models.project_management.project_template import ProjectTemplate

    # Fitness imports temporarily disabled to fix SQLAlchemy conflicts
    # from langflow.services.database.models.fitness.exercise import Exercise
    # from langflow.services.database.models.fitness.muscle_group import MuscleGroup
    # from langflow.services.database.models.fitness.equipment import Equipment
    # from langflow.services.database.models.fitness.workout import Workout
    # from langflow.services.database.models.fitness.workout_program import WorkoutProgram
    # from langflow.services.database.models.fitness.workout_session import WorkoutSession
    # from langflow.services.database.models.fitness.personal_record import PersonalRecord
    # from langflow.services.database.models.fitness.body_measurement import BodyMeasurement
    # from langflow.services.database.models.fitness.fitness_goal import FitnessGoal
    # from langflow.services.database.models.fitness.program_session import ProgramSession




class UserOptin(BaseModel):
    github_starred: bool = Field(default=False)
    dialog_dismissed: bool = Field(default=False)
    discord_clicked: bool = Field(default=False)
    # Add more opt-in actions as needed


class User(SQLModel, table=True):  # type: ignore[call-arg]
    id: UUIDstr = Field(default_factory=uuid4, primary_key=True, unique=True)
    username: str = Field(index=True, unique=True)
    password: str = Field()
    profile_image: str | None = Field(default=None, nullable=True)
    is_active: bool = Field(default=False)
    is_superuser: bool = Field(default=False)
    create_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_login_at: datetime | None = Field(default=None, nullable=True)
    # External Auth integration
    supabase_user_id: str | None = Field(default=None, nullable=True, index=True)
    firebase_user_id: str | None = Field(default=None, nullable=True, index=True)
    api_keys: list["ApiKey"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"},
    )
    store_api_key: str | None = Field(default=None, nullable=True)
    flows: list["Flow"] = Relationship(back_populates="user")
    variables: list["Variable"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"},
    )
    folders: list["Folder"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"},
    )
    # Workspace relationships
    owned_workspaces: list["Workspace"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={"cascade": "delete"},
    )
    workspace_memberships: list["WorkspaceMember"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"},
    )
    optins: Dict[str, Any] | None = Field(
        sa_column=Column(JSON, default=lambda: UserOptin().model_dump(), nullable=True)
    )

    # CRM relationships
    created_clients: list["Client"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "Client.created_by", "cascade": "delete"},
    )
    created_companies: list["Company"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "Company.created_by", "cascade": "delete"},
    )
    created_contact_relationships: list["ContactRelationship"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ContactRelationship.created_by", "cascade": "delete"},
    )
    created_invoices: list["Invoice"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "Invoice.created_by", "cascade": "delete"},
    )
    created_opportunities: list["Opportunity"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "Opportunity.created_by", "cascade": "delete"},
    )
    created_crm_tasks: list["Task"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "[Task.created_by]", "cascade": "delete"},
    )
    assigned_crm_tasks: list["Task"] = Relationship(
        back_populates="assignee",
        sa_relationship_kwargs={"foreign_keys": "[Task.assigned_to]", "cascade": "delete"},
    )
    created_products: list["Product"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "Product.created_by", "cascade": "delete"},
    )
    created_product_categories: list["ProductCategory"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ProductCategory.created_by", "cascade": "delete"},
    )
    created_product_attributes: list["ProductAttribute"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ProductAttribute.created_by", "cascade": "delete"},
    )
    created_product_reviews: list["ProductReview"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ProductReview.created_by", "cascade": "delete"},
    )
    
    # Communication tracking relationships
    logged_communications: list["CommunicationLog"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "CommunicationLog.user_id", "cascade": "delete"}
    )
    created_communication_templates: list["CommunicationTemplate"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "CommunicationTemplate.created_by", "cascade": "delete"}
    )
    assigned_follow_ups: list["FollowUpReminder"] = Relationship(
        back_populates="assignee",
        sa_relationship_kwargs={"foreign_keys": "FollowUpReminder.assigned_to", "cascade": "delete"}
    )
    created_follow_ups: list["FollowUpReminder"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "FollowUpReminder.created_by", "cascade": "delete"}
    )

    # Book Creator relationships
    books: list["Book"] = Relationship(back_populates="user", sa_relationship_kwargs={"cascade": "delete"})
    book_templates: list["BookTemplate"] = Relationship(
        sa_relationship_kwargs={"primaryjoin": "User.id == BookTemplate.user_id", "cascade": "delete"}
    )

    # Book Collaboration relationships
    book_collaborations: list["BookCollaboration"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "BookCollaboration.user_id", "cascade": "delete"}
    )
    book_comments: list["BookComment"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    book_sessions: list["BookSession"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )


    # Template Comment and Feedback relationships
    template_comments: list["TemplateComment"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    template_ratings: list["TemplateRating"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    template_feedback: list["TemplateFeedback"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )

    # Template Approval Workflow relationships
    created_approval_workflows: list["TemplateApprovalWorkflow"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "TemplateApprovalWorkflow.created_by", "cascade": "delete"}
    )
    submitted_approval_requests: list["TemplateApprovalRequest"] = Relationship(
        back_populates="submitter",
        sa_relationship_kwargs={"foreign_keys": "TemplateApprovalRequest.submitted_by", "cascade": "delete"}
    )
    template_approvals: list["TemplateApproval"] = Relationship(
        back_populates="approver",
        sa_relationship_kwargs={"foreign_keys": "TemplateApproval.approver_id", "cascade": "delete"}
    )
    approval_notifications: list["TemplateApprovalNotification"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )

    # Template Analytics relationships
    template_usage: list["TemplateUsage"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    generated_template_reports: list["TemplateReport"] = Relationship(
        back_populates="generator",
        sa_relationship_kwargs={"foreign_keys": "TemplateReport.generated_by", "cascade": "delete"}
    )


    # Social Media relationships
    created_social_accounts: list["SocialMediaAccount"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "SocialMediaAccount.created_by", "cascade": "delete"}
    )
    created_posts: list["SocialPost"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "SocialPost.created_by", "cascade": "delete"}
    )
    approved_posts: list["SocialPost"] = Relationship(
        back_populates="approver",
        sa_relationship_kwargs={"foreign_keys": "SocialPost.approved_by", "cascade": "delete"}
    )
    created_content: list["ContentLibraryItem"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ContentLibraryItem.created_by", "cascade": "delete"}
    )
    created_post_comments: list["PostComment"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "PostComment.created_by", "cascade": "delete"}
    )

    # Design Editor relationships
    design_projects: list["DesignProject"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    design_templates: list["DesignTemplate"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    design_assets: list["DesignAsset"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    design_versions: list["DesignVersion"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )

    # Video Editor relationships
    video_projects: list["VideoProject"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )
    video_templates: list["VideoTemplate"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "delete"}
    )

    # Project Management relationships
    owned_projects: list["Project"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={"foreign_keys": "Project.project_owner_id", "cascade": "delete"}
    )
    created_projects: list["Project"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "Project.created_by", "cascade": "delete"}
    )
    updated_projects: list["Project"] = Relationship(
        back_populates="updater",
        sa_relationship_kwargs={"foreign_keys": "Project.updated_by", "cascade": "delete"}
    )
    assigned_project_tasks: list["ProjectTask"] = Relationship(
        back_populates="assignee",
        sa_relationship_kwargs={"foreign_keys": "ProjectTask.assigned_to", "cascade": "delete"}
    )
    created_project_tasks: list["ProjectTask"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ProjectTask.created_by", "cascade": "delete"}
    )
    updated_project_tasks: list["ProjectTask"] = Relationship(
        back_populates="updater",
        sa_relationship_kwargs={"foreign_keys": "ProjectTask.updated_by", "cascade": "delete"}
    )
    created_project_sprints: list["Sprint"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "Sprint.created_by", "cascade": "delete"}
    )
    updated_project_sprints: list["Sprint"] = Relationship(
        back_populates="updater",
        sa_relationship_kwargs={"foreign_keys": "Sprint.updated_by", "cascade": "delete"}
    )
    project_memberships: list["ProjectMember"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "ProjectMember.user_id", "cascade": "delete"}
    )
    created_project_memberships: list["ProjectMember"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ProjectMember.created_by", "cascade": "delete"}
    )
    project_time_entries: list["TimeEntry"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "TimeEntry.user_id", "cascade": "delete"}
    )
    created_project_time_entries: list["TimeEntry"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "TimeEntry.created_by", "cascade": "delete"}
    )
    updated_project_time_entries: list["TimeEntry"] = Relationship(
        back_populates="updater",
        sa_relationship_kwargs={"foreign_keys": "TimeEntry.updated_by", "cascade": "delete"}
    )
    created_project_task_dependencies: list["TaskDependency"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "TaskDependency.created_by", "cascade": "delete"}
    )
    created_project_templates: list["ProjectTemplate"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "ProjectTemplate.created_by", "cascade": "delete"}
    )
    updated_project_templates: list["ProjectTemplate"] = Relationship(
        back_populates="updater",
        sa_relationship_kwargs={"foreign_keys": "ProjectTemplate.updated_by", "cascade": "delete"}
    )

    # Fitness relationships - temporarily disabled to fix SQLAlchemy conflicts
    # created_fitness_exercises: list["Exercise"] = Relationship(
    #     back_populates="creator",
    #     sa_relationship_kwargs={"foreign_keys": "Exercise.created_by", "cascade": "delete"}
    # )
    # created_fitness_muscle_groups: list["MuscleGroup"] = Relationship(
    #     back_populates="creator",
    #     sa_relationship_kwargs={"foreign_keys": "MuscleGroup.created_by", "cascade": "delete"}
    # )
    # created_fitness_equipment: list["Equipment"] = Relationship(
    #     back_populates="creator",
    #     sa_relationship_kwargs={"foreign_keys": "Equipment.created_by", "cascade": "delete"}
    # )
    # created_fitness_workouts: list["Workout"] = Relationship(
    #     back_populates="creator",
    #     sa_relationship_kwargs={"foreign_keys": "Workout.created_by", "cascade": "delete"}
    # )
    # created_fitness_workout_programs: list["WorkoutProgram"] = Relationship(
    #     back_populates="creator",
    #     sa_relationship_kwargs={"foreign_keys": "WorkoutProgram.created_by", "cascade": "delete"}
    # )

    # Fitness progress tracking relationships - temporarily disabled to fix SQLAlchemy conflicts
    # fitness_workout_sessions: list["WorkoutSession"] = Relationship(
    #     back_populates="user",
    #     sa_relationship_kwargs={"foreign_keys": "WorkoutSession.user_id", "cascade": "delete"}
    # )
    # fitness_personal_records: list["PersonalRecord"] = Relationship(
    #     back_populates="user",
    #     sa_relationship_kwargs={"foreign_keys": "PersonalRecord.user_id", "cascade": "delete"}
    # )
    # fitness_body_measurements: list["BodyMeasurement"] = Relationship(
    #     back_populates="user",
    #     sa_relationship_kwargs={"foreign_keys": "BodyMeasurement.user_id", "cascade": "delete"}
    # )
    # fitness_goals: list["FitnessGoal"] = Relationship(
    #     back_populates="user",
    #     sa_relationship_kwargs={"foreign_keys": "FitnessGoal.user_id", "cascade": "delete"}
    # )
    # fitness_program_sessions: list["ProgramSession"] = Relationship(
    #     back_populates="user",
    #     sa_relationship_kwargs={"foreign_keys": "ProgramSession.user_id", "cascade": "delete"}
    # )


class UserCreate(SQLModel):
    username: str = Field()
    password: str = Field()
    optins: Dict[str, Any] | None = Field(
        default={"github_starred": False, "dialog_dismissed": False, "discord_clicked": False}
    )


class UserRead(SQLModel):
    id: UUID = Field(default_factory=uuid4)
    username: str = Field()
    profile_image: str | None = Field()
    store_api_key: str | None = Field(nullable=True)
    is_active: bool = Field()
    is_superuser: bool = Field()
    create_at: datetime = Field()
    updated_at: datetime = Field()
    last_login_at: datetime | None = Field(nullable=True)
    supabase_user_id: str | None = Field(default=None, nullable=True)
    firebase_user_id: str | None = Field(default=None, nullable=True)
    optins: Dict[str, Any] | None = Field(default=None)


class UserUpdate(SQLModel):
    username: str | None = None
    profile_image: str | None = None
    password: str | None = None
    is_active: bool | None = None
    is_superuser: bool | None = None
    last_login_at: datetime | None = None
    supabase_user_id: str | None = None
    firebase_user_id: str | None = None
    optins: Dict[str, Any] | None = None
