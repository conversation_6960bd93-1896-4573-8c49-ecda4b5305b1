from datetime import datetime, timezone
from typing import TYPE_CHECKING, Dict, Any
from uuid import uuid4

from sqlalchemy import Column, JSO<PERSON>
from sqlmodel import Field, Relationship, SQLModel

from langflow.schema.serialize import UUIDstr

if TYPE_CHECKING:
    from langflow.services.database.models.design.design_project import DesignProject


class DesignElementBase(SQLModel):
    """Base model for DesignElement."""
    element_type: str = Field(index=True)  # text, image, shape, icon, etc.
    x: float = Field(default=0)
    y: float = Field(default=0)
    width: float = Field(default=100)
    height: float = Field(default=100)
    rotation: float = Field(default=0)
    opacity: float = Field(default=1.0)
    z_index: int = Field(default=0, index=True)
    locked: bool = Field(default=False)
    visible: bool = Field(default=True)
    properties: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))  # element-specific properties
    style: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))  # colors, fonts, borders, etc.
    animations: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSON))  # animation properties


class DesignElement(DesignElementBase, table=True):  # type: ignore[call-arg]
    """DesignElement model for database."""
    __tablename__ = "design_element"

    id: UUIDstr = Field(default_factory=uuid4, primary_key=True, unique=True)
    project_id: UUIDstr = Field(index=True, foreign_key="design_project.id")
    parent_id: UUIDstr | None = Field(default=None, foreign_key="design_element.id", nullable=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Relationships
    project: "DesignProject" = Relationship(back_populates="elements")
    parent: "DesignElement" = Relationship(
        back_populates="children",
        sa_relationship_kwargs={
            "remote_side": "DesignElement.id",
            "foreign_keys": "[DesignElement.parent_id]"
        }
    )
    children: list["DesignElement"] = Relationship(
        back_populates="parent",
        sa_relationship_kwargs={
            "foreign_keys": "[DesignElement.parent_id]",
            "cascade": "delete"
        }
    )


class DesignElementCreate(DesignElementBase):
    """Model for creating a new design element."""
    project_id: UUIDstr
    parent_id: UUIDstr | None = None


class DesignElementRead(DesignElementBase):
    """Model for reading a design element."""
    id: UUIDstr
    project_id: UUIDstr
    parent_id: UUIDstr | None
    created_at: datetime
    updated_at: datetime


class DesignElementUpdate(SQLModel):
    """Model for updating a design element."""
    element_type: str | None = None
    x: float | None = None
    y: float | None = None
    width: float | None = None
    height: float | None = None
    rotation: float | None = None
    opacity: float | None = None
    z_index: int | None = None
    locked: bool | None = None
    visible: bool | None = None
    properties: Dict[str, Any] | None = None
    style: Dict[str, Any] | None = None
    animations: Dict[str, Any] | None = None
    parent_id: UUIDstr | None = None
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
