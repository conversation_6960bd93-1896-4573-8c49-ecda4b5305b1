"""Firebase database provider for Langflow."""

from typing import Any, Dict, List, Optional

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from .base import DatabaseProvider

try:
    from google.cloud import firestore
    FIRESTORE_AVAILABLE = True
except ImportError:
    logger.warning("Google Cloud Firestore not available. Install with: pip install google-cloud-firestore")
    FIRESTORE_AVAILABLE = False


class FirebaseProvider(DatabaseProvider):
    """Firebase/Firestore database provider."""

    def __init__(self, project_id: str, credentials_path: Optional[str] = None, use_emulators: bool = False):
        self.project_id = project_id
        self.credentials_path = credentials_path
        self.use_emulators = use_emulators
        self.db: Optional[firestore.Client] = None
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize Firestore client."""
        if not FIRESTORE_AVAILABLE:
            raise RuntimeError("Google Cloud Firestore not available")

        try:
            import os

            if self.use_emulators:
                # Set up emulator environment variables
                os.environ['FIRESTORE_EMULATOR_HOST'] = 'localhost:8080'
                logger.info("Using Firestore emulator at localhost:8080")

                # For emulators, we don't need credentials
                self.db = firestore.Client(project=self.project_id)
            else:
                # Production mode - use credentials
                if self.credentials_path:
                    # Use service account credentials
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = self.credentials_path

                self.db = firestore.Client(project=self.project_id)

            self._initialized = True
            logger.info(f"Firebase provider initialized for project: {self.project_id} (emulators: {self.use_emulators})")
        except Exception as e:
            logger.error(f"Failed to initialize Firebase provider: {e}")
            raise

    async def create_session(self) -> AsyncSession:
        """Create a database session (not applicable for Firestore)."""
        # Firestore doesn't use sessions like SQL databases
        # This method is here for interface compatibility
        raise NotImplementedError("Firestore doesn't use sessions like SQL databases")

    async def create_collection_ref(self, collection_name: str):
        """Create a collection reference."""
        if not self.db:
            raise RuntimeError("Firebase provider not initialized")
        return self.db.collection(collection_name)

    async def get_document(self, collection: str, doc_id: str) -> Optional[Dict]:
        """Get a document from a collection."""
        if not self.db:
            raise RuntimeError("Firebase provider not initialized")

        try:
            doc_ref = self.db.collection(collection).document(doc_id)
            doc = doc_ref.get()
            return doc.to_dict() if doc.exists else None
        except Exception as e:
            logger.error(f"Failed to get document {doc_id} from {collection}: {e}")
            raise

    async def set_document(self, collection: str, doc_id: str, data: Dict[str, Any]) -> bool:
        """Set a document in a collection."""
        if not self.db:
            raise RuntimeError("Firebase provider not initialized")

        try:
            doc_ref = self.db.collection(collection).document(doc_id)
            doc_ref.set(data)
            return True
        except Exception as e:
            logger.error(f"Failed to set document {doc_id} in {collection}: {e}")
            raise

    async def update_document(self, collection: str, doc_id: str, data: Dict[str, Any]) -> bool:
        """Update a document in a collection."""
        if not self.db:
            raise RuntimeError("Firebase provider not initialized")

        try:
            doc_ref = self.db.collection(collection).document(doc_id)
            doc_ref.update(data)
            return True
        except Exception as e:
            logger.error(f"Failed to update document {doc_id} in {collection}: {e}")
            raise

    async def delete_document(self, collection: str, doc_id: str) -> bool:
        """Delete a document from a collection."""
        if not self.db:
            raise RuntimeError("Firebase provider not initialized")

        try:
            doc_ref = self.db.collection(collection).document(doc_id)
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Failed to delete document {doc_id} from {collection}: {e}")
            raise

    async def query_collection(self, collection: str, filters: Optional[List] = None,
                              limit: Optional[int] = None) -> List[Dict]:
        """Query a collection with optional filters."""
        if not self.db:
            raise RuntimeError("Firebase provider not initialized")

        try:
            query = self.db.collection(collection)

            # Apply filters if provided
            if filters:
                for filter_item in filters:
                    field, operator, value = filter_item
                    query = query.where(field, operator, value)

            # Apply limit if provided
            if limit:
                query = query.limit(limit)

            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Failed to query collection {collection}: {e}")
            raise

    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict]:
        """Execute a raw query (not applicable for Firestore)."""
        # Firestore doesn't support raw SQL queries
        raise NotImplementedError("Firestore doesn't support raw SQL queries")

    async def close(self) -> None:
        """Close the database connection."""
        # Firestore client doesn't need explicit closing
        self._initialized = False
        logger.info("Firebase provider connection closed")

    def get_connection_string(self) -> str:
        """Get the connection string for this provider."""
        return f"firestore://{self.project_id}"

    def is_available(self) -> bool:
        """Check if the provider is available and properly configured."""
        return FIRESTORE_AVAILABLE and self._initialized

    @property
    def provider_name(self) -> str:
        """Get the name of this provider."""
        return "firebase"


# Global provider instance
_firebase_provider = None


def get_firebase_provider():
    """Get or create Firebase provider instance.

    Returns:
        FirebaseProvider: Firebase provider instance
    """
    global _firebase_provider

    if _firebase_provider is None:
        import os

        # Get configuration from environment variables
        project_id = os.getenv("FIREBASE_PROJECT_ID", "langflow-dev")
        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        use_emulators = os.getenv("USE_FIREBASE_EMULATORS", "true").lower() == "true"

        _firebase_provider = FirebaseProvider(
            project_id=project_id,
            credentials_path=credentials_path,
            use_emulators=use_emulators
        )

        # Initialize the provider
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(_firebase_provider.initialize())
        except RuntimeError:
            # If no event loop is running, create a new one
            asyncio.run(_firebase_provider.initialize())

    return _firebase_provider
