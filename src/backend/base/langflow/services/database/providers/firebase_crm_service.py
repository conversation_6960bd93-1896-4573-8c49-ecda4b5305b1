"""Firebase CRM service for handling CRM operations with Firestore."""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional
from uuid import uuid4

from langflow.services.database.models.crm.client import Client<PERSON>reate, ClientRead, ClientUpdate
from langflow.services.database.models.crm.company import CompanyCreate, CompanyRead, CompanyUpdate, CompanyHierarchy
from langflow.services.database.models.crm.contact_relationship import ContactRelationshipCreate, ContactRelationshipRead, ContactRelationshipUpdate, OrgChartNode, RelationshipNetwork
from langflow.services.database.models.crm.communication_log import (
    CommunicationLogCreate, CommunicationLogRead, CommunicationLogUpdate, 
    CommunicationTemplateCreate, CommunicationTemplateRead, CommunicationTemplateUpdate,
    FollowUpReminderCreate, FollowUpReminderRead, FollowUpReminderUpdate,
    CommunicationTimelineView, CommunicationAnalytics
)
from langflow.services.database.models.crm.invoice import InvoiceCreate, InvoiceRead, InvoiceUpdate
from langflow.services.database.models.crm.opportunity import Opportunity<PERSON><PERSON>, OpportunityRead, OpportunityUpdate
from langflow.services.database.models.crm.task import TaskCreate, TaskRead, TaskUpdate
from langflow.services.database.models.crm.product import ProductCreate, ProductRead, ProductUpdate

logger = logging.getLogger(__name__)


class FirebaseCRMService:
    """Service for handling CRM operations with Firebase Firestore."""
    
    def __init__(self, db_provider):
        """Initialize Firebase CRM service.
        
        Args:
            db_provider: Firebase database provider instance
        """
        self.db = db_provider.db
        self.clients_collection = "crm_clients"
        self.companies_collection = "crm_companies"
        self.contact_relationships_collection = "crm_contact_relationships"
        self.communications_collection = "crm_communications"
        self.communication_templates_collection = "crm_communication_templates"
        self.follow_up_reminders_collection = "crm_follow_up_reminders"
        self.invoices_collection = "crm_invoices"
        self.opportunities_collection = "crm_opportunities"
        self.tasks_collection = "crm_tasks"
        self.products_collection = "crm_products"
    
    # Client operations
    async def get_clients(self, workspace_id: str, status: Optional[str] = None, 
                         limit: Optional[int] = None) -> List[Dict]:
        """Get clients from Firestore.
        
        Args:
            workspace_id: Workspace ID to filter by
            status: Optional status filter
            limit: Optional limit for results
            
        Returns:
            List of client documents
        """
        try:
            query = self.db.collection(self.clients_collection)
            
            # Filter by workspace
            query = query.where("workspace_id", "==", workspace_id)
            
            # Filter by status if provided
            if status:
                query = query.where("status", "==", status)
            
            # Apply limit if provided
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting clients: {e}")
            return []
    
    async def get_client(self, client_id: str) -> Optional[Dict]:
        """Get a single client by ID.
        
        Args:
            client_id: Client ID
            
        Returns:
            Client document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.clients_collection).document(client_id)
            doc = doc_ref.get()
            
            if doc.exists:
                return {"id": doc.id, **doc.to_dict()}
            return None
        except Exception as e:
            logger.error(f"Error getting client {client_id}: {e}")
            return None
    
    async def create_client(self, client_data: ClientCreate, user_id: str, 
                           workspace_id: str) -> Optional[Dict]:
        """Create a new client in Firestore.
        
        Args:
            client_data: Client creation data
            user_id: User ID creating the client
            workspace_id: Workspace ID
            
        Returns:
            Created client document if successful, None otherwise
        """
        try:
            client_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            client_dict = client_data.model_dump()
            client_dict.update({
                "id": client_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            doc_ref = self.db.collection(self.clients_collection).document(client_id)
            doc_ref.set(client_dict)
            
            return {"id": client_id, **client_dict}
        except Exception as e:
            logger.error(f"Error creating client: {e}")
            return None
    
    async def update_client(self, client_id: str, client_update: ClientUpdate) -> Optional[Dict]:
        """Update a client in Firestore.
        
        Args:
            client_id: Client ID
            client_update: Client update data
            
        Returns:
            Updated client document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.clients_collection).document(client_id)
            
            # Check if client exists
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            # Update with new data
            update_data = client_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            doc_ref.update(update_data)
            
            # Get updated client
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating client {client_id}: {e}")
            return None
    
    async def delete_client(self, client_id: str) -> bool:
        """Delete a client from Firestore.
        
        Args:
            client_id: Client ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.clients_collection).document(client_id)
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting client {client_id}: {e}")
            return False
    
    # Company operations
    async def get_companies(self, workspace_id: str, status: Optional[str] = None, 
                           limit: Optional[int] = None, search: Optional[str] = None) -> List[Dict]:
        """Get companies from Firestore.
        
        Args:
            workspace_id: Workspace ID to filter by
            status: Optional status filter
            limit: Optional limit for results
            search: Optional search term for company name
            
        Returns:
            List of company documents
        """
        try:
            query = self.db.collection(self.companies_collection)
            
            # Filter by workspace
            query = query.where("workspace_id", "==", workspace_id)
            
            # Filter by status if provided
            if status:
                query = query.where("status", "==", status)
            
            # Apply limit if provided
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            companies = [{"id": doc.id, **doc.to_dict()} for doc in docs]
            
            # Apply search filter in memory (Firestore doesn't support case-insensitive search)
            if search and companies:
                search_lower = search.lower()
                companies = [
                    company for company in companies 
                    if search_lower in company.get("name", "").lower()
                ]
            
            return companies
        except Exception as e:
            logger.error(f"Error getting companies: {e}")
            return []
    
    async def get_company(self, company_id: str, workspace_id: str) -> Optional[Dict]:
        """Get a single company by ID.
        
        Args:
            company_id: Company ID
            workspace_id: Workspace ID for security
            
        Returns:
            Company document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.companies_collection).document(company_id)
            doc = doc_ref.get()
            
            if doc.exists:
                company_data = {"id": doc.id, **doc.to_dict()}
                # Verify workspace access
                if company_data.get("workspace_id") == workspace_id:
                    return company_data
            return None
        except Exception as e:
            logger.error(f"Error getting company {company_id}: {e}")
            return None
    
    async def create_company(self, company_data: CompanyCreate, user_id: str, 
                            workspace_id: str) -> Optional[Dict]:
        """Create a new company in Firestore.
        
        Args:
            company_data: Company creation data
            user_id: User ID creating the company
            workspace_id: Workspace ID
            
        Returns:
            Created company document if successful, None otherwise
        """
        try:
            company_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            company_dict = company_data.model_dump()
            company_dict.update({
                "id": company_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            # Validate hierarchy doesn't create cycles if parent_company_id is provided
            if company_dict.get("parent_company_id"):
                is_valid = await self._validate_company_hierarchy(
                    company_id, company_dict["parent_company_id"], workspace_id
                )
                if not is_valid:
                    logger.error(f"Invalid company hierarchy: would create cycle")
                    return None
            
            doc_ref = self.db.collection(self.companies_collection).document(company_id)
            doc_ref.set(company_dict)
            
            return {"id": company_id, **company_dict}
        except Exception as e:
            logger.error(f"Error creating company: {e}")
            return None
    
    async def update_company(self, company_id: str, company_update: CompanyUpdate, 
                            workspace_id: str) -> Optional[Dict]:
        """Update a company in Firestore.
        
        Args:
            company_id: Company ID
            company_update: Company update data
            workspace_id: Workspace ID for security
            
        Returns:
            Updated company document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.companies_collection).document(company_id)
            
            # Check if company exists and belongs to workspace
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            existing_data = doc.to_dict()
            if existing_data.get("workspace_id") != workspace_id:
                return None
            
            # Update with new data
            update_data = company_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            # Validate hierarchy if parent_company_id is being updated
            if "parent_company_id" in update_data and update_data["parent_company_id"]:
                is_valid = await self._validate_company_hierarchy(
                    company_id, update_data["parent_company_id"], workspace_id
                )
                if not is_valid:
                    logger.error(f"Invalid company hierarchy: would create cycle")
                    return None
            
            doc_ref.update(update_data)
            
            # Get updated company
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating company {company_id}: {e}")
            return None
    
    async def delete_company(self, company_id: str, workspace_id: str) -> bool:
        """Delete a company from Firestore.
        
        Args:
            company_id: Company ID
            workspace_id: Workspace ID for security
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.companies_collection).document(company_id)
            
            # Check if company exists and belongs to workspace
            doc = doc_ref.get()
            if not doc.exists:
                return False
            
            existing_data = doc.to_dict()
            if existing_data.get("workspace_id") != workspace_id:
                return False
            
            # Check if company has subsidiaries
            subsidiaries = await self._get_company_subsidiaries(company_id, workspace_id)
            if subsidiaries:
                logger.error(f"Cannot delete company {company_id}: has subsidiaries")
                return False
            
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting company {company_id}: {e}")
            return False
    
    async def get_company_hierarchy(self, workspace_id: str, root_company_id: Optional[str] = None) -> List[Dict]:
        """Get company hierarchy for workspace.
        
        Args:
            workspace_id: Workspace ID
            root_company_id: Optional root company ID to start hierarchy from
            
        Returns:
            List of company hierarchy data
        """
        try:
            # Get all companies in workspace
            query = self.db.collection(self.companies_collection)
            query = query.where("workspace_id", "==", workspace_id)
            docs = query.stream()
            
            companies = [{"id": doc.id, **doc.to_dict()} for doc in docs]
            
            # Build hierarchy tree
            return self._build_company_hierarchy(companies, root_company_id)
        except Exception as e:
            logger.error(f"Error getting company hierarchy: {e}")
            return []
    
    async def _validate_company_hierarchy(self, company_id: str, parent_company_id: str, 
                                         workspace_id: str) -> bool:
        """Validate that setting parent doesn't create a cycle.
        
        Args:
            company_id: Company ID
            parent_company_id: Proposed parent company ID
            workspace_id: Workspace ID
            
        Returns:
            True if valid, False if would create cycle
        """
        try:
            # Traverse up the hierarchy to check for cycles
            current_parent_id = parent_company_id
            visited = set()
            
            while current_parent_id:
                if current_parent_id == company_id or current_parent_id in visited:
                    return False  # Cycle detected
                
                visited.add(current_parent_id)
                
                # Get parent company
                parent_doc = await self.get_company(current_parent_id, workspace_id)
                if not parent_doc:
                    break
                
                current_parent_id = parent_doc.get("parent_company_id")
            
            return True
        except Exception as e:
            logger.error(f"Error validating company hierarchy: {e}")
            return False
    
    async def _get_company_subsidiaries(self, company_id: str, workspace_id: str) -> List[Dict]:
        """Get subsidiaries of a company.
        
        Args:
            company_id: Company ID
            workspace_id: Workspace ID
            
        Returns:
            List of subsidiary companies
        """
        try:
            query = self.db.collection(self.companies_collection)
            query = query.where("workspace_id", "==", workspace_id)
            query = query.where("parent_company_id", "==", company_id)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting company subsidiaries: {e}")
            return []
    
    def _build_company_hierarchy(self, companies: List[Dict], root_id: Optional[str] = None) -> List[Dict]:
        """Build hierarchical tree structure from flat company list.
        
        Args:
            companies: Flat list of company documents
            root_id: Optional root company ID
            
        Returns:
            List of hierarchical company data
        """
        # Create lookup maps
        company_map = {c["id"]: c for c in companies}
        children_map = {}
        
        # Build children map
        for company in companies:
            parent_id = company.get("parent_company_id")
            if parent_id:
                if parent_id not in children_map:
                    children_map[parent_id] = []
                children_map[parent_id].append(company["id"])
        
        def build_tree(company_id: str, level: int = 0) -> Dict:
            """Recursively build tree structure."""
            company = company_map.get(company_id, {})
            node = {
                "id": company_id,
                "name": company.get("name", ""),
                "parent_company_id": company.get("parent_company_id"),
                "level": level,
                "children": [],
                "client_count": 0,  # TODO: Calculate actual client count
                "revenue": 0.0  # TODO: Calculate actual revenue
            }
            
            # Add children
            if company_id in children_map:
                for child_id in children_map[company_id]:
                    node["children"].append(build_tree(child_id, level + 1))
            
            return node
        
        # Build hierarchy
        if root_id and root_id in company_map:
            return [build_tree(root_id)]
        else:
            # Get all root companies (no parent)
            root_companies = [c for c in companies if not c.get("parent_company_id")]
            return [build_tree(c["id"]) for c in root_companies]
    
    # Contact Relationship operations
    async def get_contact_relationships(self, workspace_id: str, contact_id: Optional[str] = None,
                                      limit: Optional[int] = None) -> List[Dict]:
        """Get contact relationships from Firestore.
        
        Args:
            workspace_id: Workspace ID to filter by
            contact_id: Optional contact ID to filter relationships
            limit: Optional limit for results
            
        Returns:
            List of contact relationship documents
        """
        try:
            query = self.db.collection(self.contact_relationships_collection)
            
            # Filter by workspace
            query = query.where("workspace_id", "==", workspace_id)
            
            # Filter by contact if provided
            if contact_id:
                # Get relationships where contact is either the primary or related contact
                query1 = query.where("contact_id", "==", contact_id)
                query2 = query.where("related_contact_id", "==", contact_id)
                
                # Execute both queries and combine results
                docs1 = query1.limit(limit // 2 if limit else 50).stream()
                docs2 = query2.limit(limit // 2 if limit else 50).stream()
                
                relationships = []
                relationships.extend([{"id": doc.id, **doc.to_dict()} for doc in docs1])
                relationships.extend([{"id": doc.id, **doc.to_dict()} for doc in docs2])
                
                # Remove duplicates and apply limit
                seen = set()
                unique_relationships = []
                for rel in relationships:
                    if rel["id"] not in seen:
                        seen.add(rel["id"])
                        unique_relationships.append(rel)
                
                return unique_relationships[:limit] if limit else unique_relationships
            
            # Apply limit if provided
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting contact relationships: {e}")
            return []
    
    async def get_contact_relationship(self, relationship_id: str, workspace_id: str) -> Optional[Dict]:
        """Get a single contact relationship by ID.
        
        Args:
            relationship_id: Relationship ID
            workspace_id: Workspace ID for security
            
        Returns:
            Contact relationship document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.contact_relationships_collection).document(relationship_id)
            doc = doc_ref.get()
            
            if doc.exists:
                relationship_data = {"id": doc.id, **doc.to_dict()}
                # Verify workspace access
                if relationship_data.get("workspace_id") == workspace_id:
                    return relationship_data
            return None
        except Exception as e:
            logger.error(f"Error getting contact relationship {relationship_id}: {e}")
            return None
    
    async def create_contact_relationship(self, relationship_data: ContactRelationshipCreate, 
                                        user_id: str, workspace_id: str) -> Optional[Dict]:
        """Create a new contact relationship in Firestore.
        
        Args:
            relationship_data: Contact relationship creation data
            user_id: User ID creating the relationship
            workspace_id: Workspace ID
            
        Returns:
            Created contact relationship document if successful, None otherwise
        """
        try:
            relationship_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            relationship_dict = relationship_data.model_dump()
            relationship_dict.update({
                "id": relationship_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            # Validate both contacts exist and belong to workspace
            contact1 = await self.get_client(relationship_dict["contact_id"])
            contact2 = await self.get_client(relationship_dict["related_contact_id"])
            
            if not contact1 or not contact2:
                logger.error(f"One or both contacts not found")
                return None
            
            if contact1.get("workspace_id") != workspace_id or contact2.get("workspace_id") != workspace_id:
                logger.error(f"Contacts do not belong to workspace {workspace_id}")
                return None
            
            # Prevent self-relationships
            if relationship_dict["contact_id"] == relationship_dict["related_contact_id"]:
                logger.error(f"Cannot create relationship between contact and itself")
                return None
            
            doc_ref = self.db.collection(self.contact_relationships_collection).document(relationship_id)
            doc_ref.set(relationship_dict)
            
            # Create bidirectional relationship if specified
            if relationship_dict.get("is_bidirectional", False):
                await self._create_reverse_relationship(relationship_dict, user_id, workspace_id)
            
            # Calculate and update relationship strength
            strength = await self._calculate_relationship_strength(
                relationship_dict["contact_id"], 
                relationship_dict["related_contact_id"],
                workspace_id
            )
            if strength is not None:
                doc_ref.update({"relationship_strength": strength})
                relationship_dict["relationship_strength"] = strength
            
            return {"id": relationship_id, **relationship_dict}
        except Exception as e:
            logger.error(f"Error creating contact relationship: {e}")
            return None
    
    async def update_contact_relationship(self, relationship_id: str, 
                                        relationship_update: ContactRelationshipUpdate,
                                        workspace_id: str) -> Optional[Dict]:
        """Update a contact relationship in Firestore.
        
        Args:
            relationship_id: Relationship ID
            relationship_update: Relationship update data
            workspace_id: Workspace ID for security
            
        Returns:
            Updated contact relationship document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.contact_relationships_collection).document(relationship_id)
            
            # Check if relationship exists and belongs to workspace
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            existing_data = doc.to_dict()
            if existing_data.get("workspace_id") != workspace_id:
                return None
            
            # Update with new data
            update_data = relationship_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            doc_ref.update(update_data)
            
            # Get updated relationship
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating contact relationship {relationship_id}: {e}")
            return None
    
    async def delete_contact_relationship(self, relationship_id: str, workspace_id: str) -> bool:
        """Delete a contact relationship from Firestore.
        
        Args:
            relationship_id: Relationship ID
            workspace_id: Workspace ID for security
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.contact_relationships_collection).document(relationship_id)
            
            # Check if relationship exists and belongs to workspace
            doc = doc_ref.get()
            if not doc.exists:
                return False
            
            existing_data = doc.to_dict()
            if existing_data.get("workspace_id") != workspace_id:
                return False
            
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting contact relationship {relationship_id}: {e}")
            return False
    
    async def get_org_chart(self, workspace_id: str, company_id: Optional[str] = None) -> List[Dict]:
        """Get organization chart for workspace or company.
        
        Args:
            workspace_id: Workspace ID
            company_id: Optional company ID to filter by
            
        Returns:
            List of org chart nodes
        """
        try:
            # Get all contacts in workspace/company
            clients_query = self.db.collection(self.clients_collection)
            clients_query = clients_query.where("workspace_id", "==", workspace_id)
            if company_id:
                clients_query = clients_query.where("company_id", "==", company_id)
            
            clients_docs = clients_query.stream()
            clients = [{"id": doc.id, **doc.to_dict()} for doc in clients_docs]
            
            # Get all relationships for these contacts
            relationships = await self.get_contact_relationships(workspace_id)
            
            # Filter relationships to only include contacts in our client list
            client_ids = {client["id"] for client in clients}
            filtered_relationships = [
                rel for rel in relationships 
                if rel.get("contact_id") in client_ids and rel.get("related_contact_id") in client_ids
            ]
            
            # Build org chart tree
            return self._build_org_chart(clients, filtered_relationships)
        except Exception as e:
            logger.error(f"Error getting org chart: {e}")
            return []
    
    async def get_relationship_network(self, workspace_id: str, center_contact_id: str) -> Dict:
        """Get relationship network for a specific contact.
        
        Args:
            workspace_id: Workspace ID
            center_contact_id: Central contact ID for network analysis
            
        Returns:
            Network data with nodes and edges
        """
        try:
            # Get relationships for the center contact
            relationships = await self.get_contact_relationships(workspace_id, center_contact_id)
            
            # Get all related contacts
            contact_ids = set([center_contact_id])
            for rel in relationships:
                contact_ids.add(rel.get("contact_id"))
                contact_ids.add(rel.get("related_contact_id"))
            
            # Get contact details
            nodes = []
            for contact_id in contact_ids:
                contact = await self.get_client(contact_id)
                if contact and contact.get("workspace_id") == workspace_id:
                    nodes.append({
                        "id": contact_id,
                        "name": contact.get("name", ""),
                        "email": contact.get("email", ""),
                        "company": contact.get("company", ""),
                        "is_center": contact_id == center_contact_id
                    })
            
            # Build edges from relationships
            edges = []
            for rel in relationships:
                edges.append({
                    "source": rel.get("contact_id"),
                    "target": rel.get("related_contact_id"),
                    "relationship_type": rel.get("relationship_type"),
                    "strength": rel.get("relationship_strength", 0.0),
                    "influence_level": rel.get("influence_level", 5)
                })
            
            # Calculate network metrics
            metrics = self._calculate_network_metrics(nodes, edges)
            
            return {
                "workspace_id": workspace_id,
                "center_contact_id": center_contact_id,
                "nodes": nodes,
                "edges": edges,
                "metrics": metrics
            }
        except Exception as e:
            logger.error(f"Error getting relationship network: {e}")
            return {}
    
    async def _create_reverse_relationship(self, original_rel: Dict, user_id: str, workspace_id: str):
        """Create reverse relationship for bidirectional relationships."""
        try:
            reverse_id = str(uuid4())
            reverse_rel = {
                "id": reverse_id,
                "contact_id": original_rel["related_contact_id"],
                "related_contact_id": original_rel["contact_id"],
                "relationship_type": self._get_reverse_relationship_type(original_rel["relationship_type"]),
                "company_role": original_rel.get("company_role"),
                "influence_level": original_rel.get("influence_level", 5),
                "relationship_strength": original_rel.get("relationship_strength", 0.0),
                "notes": f"Reverse of: {original_rel.get('notes', '')}",
                "is_bidirectional": False,  # Avoid infinite loop
                "status": original_rel.get("status", "active"),
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            doc_ref = self.db.collection(self.contact_relationships_collection).document(reverse_id)
            doc_ref.set(reverse_rel)
        except Exception as e:
            logger.error(f"Error creating reverse relationship: {e}")
    
    def _get_reverse_relationship_type(self, relationship_type: str) -> str:
        """Get the reverse relationship type."""
        reverse_mapping = {
            "reports_to": "manages",
            "manages": "reports_to",
            "influences": "influenced_by",
            "influenced_by": "influences",
            "colleague": "colleague",
            "decision_maker": "seeks_approval_from"
        }
        return reverse_mapping.get(relationship_type, relationship_type)
    
    async def _calculate_relationship_strength(self, contact_id: str, related_contact_id: str, 
                                             workspace_id: str) -> Optional[float]:
        """Calculate relationship strength based on interactions."""
        try:
            # This is a simplified calculation - in reality, you'd analyze:
            # - Communication frequency
            # - Interaction types
            # - Response times
            # - Meeting frequency
            # For now, return a base score that can be enhanced later
            base_strength = 0.5
            
            # Could be enhanced with communication tracking data
            # communication_count = await self.get_communication_count(contact_id, related_contact_id)
            # interaction_frequency = await self.get_interaction_frequency(contact_id, related_contact_id)
            # strength = min(base_strength + (communication_count * 0.01) + (interaction_frequency * 0.1), 1.0)
            
            return base_strength
        except Exception as e:
            logger.error(f"Error calculating relationship strength: {e}")
            return None
    
    def _build_org_chart(self, clients: List[Dict], relationships: List[Dict]) -> List[Dict]:
        """Build organization chart from clients and relationships."""
        # Create lookup maps
        client_map = {c["id"]: c for c in clients}
        subordinates_map = {}
        
        # Build subordinates map from "reports_to" relationships
        for rel in relationships:
            if rel.get("relationship_type") == "reports_to":
                manager_id = rel.get("related_contact_id")
                subordinate_id = rel.get("contact_id")
                
                if manager_id not in subordinates_map:
                    subordinates_map[manager_id] = []
                subordinates_map[manager_id].append(subordinate_id)
        
        def build_node(client_id: str, level: int = 0) -> Dict:
            """Recursively build org chart node."""
            client = client_map.get(client_id, {})
            node = {
                "contact_id": client_id,
                "contact_name": client.get("name", ""),
                "contact_email": client.get("email", ""),
                "company_role": None,  # Could be derived from relationships
                "influence_level": 5,  # Default value
                "level": level,
                "children": [],
                "subordinate_count": len(subordinates_map.get(client_id, []))
            }
            
            # Add children
            if client_id in subordinates_map:
                for subordinate_id in subordinates_map[client_id]:
                    node["children"].append(build_node(subordinate_id, level + 1))
            
            return node
        
        # Find root nodes (people with no manager)
        managed_ids = set()
        for rel in relationships:
            if rel.get("relationship_type") == "reports_to":
                managed_ids.add(rel.get("contact_id"))
        
        root_clients = [c for c in clients if c["id"] not in managed_ids]
        return [build_node(c["id"]) for c in root_clients]
    
    def _calculate_network_metrics(self, nodes: List[Dict], edges: List[Dict]) -> Dict[str, float]:
        """Calculate basic network metrics."""
        node_count = len(nodes)
        edge_count = len(edges)
        
        if node_count == 0:
            return {"density": 0.0, "avg_connections": 0.0}
        
        # Calculate network density
        max_possible_edges = node_count * (node_count - 1) / 2
        density = edge_count / max_possible_edges if max_possible_edges > 0 else 0.0
        
        # Calculate average connections per node
        avg_connections = (edge_count * 2) / node_count if node_count > 0 else 0.0
        
        return {
            "density": density,
            "avg_connections": avg_connections,
            "node_count": node_count,
            "edge_count": edge_count
        }
    
    # Invoice operations
    async def get_invoices(self, workspace_id: str, status: Optional[str] = None,
                          limit: Optional[int] = None) -> List[Dict]:
        """Get invoices from Firestore."""
        try:
            query = self.db.collection(self.invoices_collection)
            query = query.where("workspace_id", "==", workspace_id)
            
            if status:
                query = query.where("status", "==", status)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting invoices: {e}")
            return []
    
    async def get_invoice(self, invoice_id: str) -> Optional[Dict]:
        """Get a single invoice by ID.
        
        Args:
            invoice_id: Invoice ID
            
        Returns:
            Invoice document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.invoices_collection).document(invoice_id)
            doc = doc_ref.get()
            
            if doc.exists:
                return {"id": doc.id, **doc.to_dict()}
            return None
        except Exception as e:
            logger.error(f"Error getting invoice {invoice_id}: {e}")
            return None
    
    async def create_invoice(self, invoice_data: InvoiceCreate, user_id: str,
                            workspace_id: str) -> Optional[Dict]:
        """Create a new invoice in Firestore."""
        try:
            invoice_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            invoice_dict = invoice_data.model_dump()
            invoice_dict.update({
                "id": invoice_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            doc_ref = self.db.collection(self.invoices_collection).document(invoice_id)
            doc_ref.set(invoice_dict)
            
            return {"id": invoice_id, **invoice_dict}
        except Exception as e:
            logger.error(f"Error creating invoice: {e}")
            return None
    
    async def update_invoice(self, invoice_id: str, invoice_update: InvoiceUpdate) -> Optional[Dict]:
        """Update an invoice in Firestore.
        
        Args:
            invoice_id: Invoice ID
            invoice_update: Invoice update data
            
        Returns:
            Updated invoice document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.invoices_collection).document(invoice_id)
            
            # Check if invoice exists
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            # Update with new data
            update_data = invoice_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            doc_ref.update(update_data)
            
            # Get updated invoice
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating invoice {invoice_id}: {e}")
            return None
    
    async def delete_invoice(self, invoice_id: str) -> bool:
        """Delete an invoice from Firestore.
        
        Args:
            invoice_id: Invoice ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.invoices_collection).document(invoice_id)
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting invoice {invoice_id}: {e}")
            return False
    
    # Opportunity operations
    async def get_opportunities(self, workspace_id: str, status: Optional[str] = None,
                               limit: Optional[int] = None) -> List[Dict]:
        """Get opportunities from Firestore."""
        try:
            query = self.db.collection(self.opportunities_collection)
            query = query.where("workspace_id", "==", workspace_id)

            if status:
                query = query.where("status", "==", status)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting opportunities: {e}")
            return []
    
    async def get_opportunity(self, opportunity_id: str) -> Optional[Dict]:
        """Get a single opportunity by ID.
        
        Args:
            opportunity_id: Opportunity ID
            
        Returns:
            Opportunity document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.opportunities_collection).document(opportunity_id)
            doc = doc_ref.get()
            
            if doc.exists:
                return {"id": doc.id, **doc.to_dict()}
            return None
        except Exception as e:
            logger.error(f"Error getting opportunity {opportunity_id}: {e}")
            return None
    
    async def create_opportunity(self, opportunity_data: OpportunityCreate, user_id: str,
                                workspace_id: str) -> Optional[Dict]:
        """Create a new opportunity in Firestore."""
        try:
            opportunity_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            opportunity_dict = opportunity_data.model_dump()
            opportunity_dict.update({
                "id": opportunity_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            doc_ref = self.db.collection(self.opportunities_collection).document(opportunity_id)
            doc_ref.set(opportunity_dict)
            
            return {"id": opportunity_id, **opportunity_dict}
        except Exception as e:
            logger.error(f"Error creating opportunity: {e}")
            return None
    
    async def update_opportunity(self, opportunity_id: str, opportunity_update: OpportunityUpdate) -> Optional[Dict]:
        """Update an opportunity in Firestore.
        
        Args:
            opportunity_id: Opportunity ID
            opportunity_update: Opportunity update data
            
        Returns:
            Updated opportunity document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.opportunities_collection).document(opportunity_id)
            
            # Check if opportunity exists
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            # Update with new data
            update_data = opportunity_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            doc_ref.update(update_data)
            
            # Get updated opportunity
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating opportunity {opportunity_id}: {e}")
            return None
    
    async def delete_opportunity(self, opportunity_id: str) -> bool:
        """Delete an opportunity from Firestore.
        
        Args:
            opportunity_id: Opportunity ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.opportunities_collection).document(opportunity_id)
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting opportunity {opportunity_id}: {e}")
            return False
    
    # Task operations
    async def get_tasks(self, workspace_id: str, status: Optional[str] = None,
                       limit: Optional[int] = None) -> List[Dict]:
        """Get tasks from Firestore."""
        try:
            query = self.db.collection(self.tasks_collection)
            query = query.where("workspace_id", "==", workspace_id)

            if status:
                query = query.where("status", "==", status)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting tasks: {e}")
            return []
    
    async def get_task(self, task_id: str) -> Optional[Dict]:
        """Get a single task by ID.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.tasks_collection).document(task_id)
            doc = doc_ref.get()
            
            if doc.exists:
                return {"id": doc.id, **doc.to_dict()}
            return None
        except Exception as e:
            logger.error(f"Error getting task {task_id}: {e}")
            return None
    
    async def create_task(self, task_data: TaskCreate, user_id: str,
                         workspace_id: str) -> Optional[Dict]:
        """Create a new task in Firestore."""
        try:
            task_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            task_dict = task_data.model_dump()
            task_dict.update({
                "id": task_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            doc_ref = self.db.collection(self.tasks_collection).document(task_id)
            doc_ref.set(task_dict)
            
            return {"id": task_id, **task_dict}
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return None
    
    async def update_task(self, task_id: str, task_update: TaskUpdate) -> Optional[Dict]:
        """Update a task in Firestore.
        
        Args:
            task_id: Task ID
            task_update: Task update data
            
        Returns:
            Updated task document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.tasks_collection).document(task_id)
            
            # Check if task exists
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            # Update with new data
            update_data = task_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            doc_ref.update(update_data)
            
            # Get updated task
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating task {task_id}: {e}")
            return None
    
    async def delete_task(self, task_id: str) -> bool:
        """Delete a task from Firestore.
        
        Args:
            task_id: Task ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.tasks_collection).document(task_id)
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting task {task_id}: {e}")
            return False
    
    # Dashboard operations
    async def get_workspace_stats(self, workspace_id: str) -> Dict:
        """Get workspace statistics for dashboard."""
        try:
            stats = {
                "clients": {"total": 0, "active": 0},
                "invoices": {"total": 0, "revenue": 0.0},
                "opportunities": {"total": 0, "open_value": 0.0},
                "tasks": {"open": 0, "in_progress": 0, "completed": 0}
            }
            
            # Get client stats
            clients = await self.get_clients(workspace_id)
            stats["clients"]["total"] = len(clients)
            stats["clients"]["active"] = len([c for c in clients if c.get("status") == "active"])
            
            # Get invoice stats
            invoices = await self.get_invoices(workspace_id)
            stats["invoices"]["total"] = len(invoices)
            stats["invoices"]["revenue"] = sum(i.get("amount", 0) for i in invoices if i.get("status") == "paid")
            
            # Get opportunity stats
            opportunities = await self.get_opportunities(workspace_id)
            stats["opportunities"]["total"] = len(opportunities)
            stats["opportunities"]["open_value"] = sum(o.get("value", 0) for o in opportunities if o.get("status") in ["new", "qualified", "proposal", "negotiation"])
            
            # Get task stats
            tasks = await self.get_tasks(workspace_id)
            stats["tasks"]["open"] = len([t for t in tasks if t.get("status") == "open"])
            stats["tasks"]["in_progress"] = len([t for t in tasks if t.get("status") == "in_progress"])
            stats["tasks"]["completed"] = len([t for t in tasks if t.get("status") == "completed"])
            
            return stats
        except Exception as e:
            logger.error(f"Error getting workspace stats: {e}")
            return {}
    
    async def get_client_distribution(self, workspace_id: str) -> Dict:
        """Get client distribution data for dashboard charts.
        
        Args:
            workspace_id: Workspace ID
            
        Returns:
            Client distribution data with status counts and other metrics
        """
        try:
            clients = await self.get_clients(workspace_id)
            
            # Calculate distribution by status
            status_distribution = {}
            industry_distribution = {}
            
            for client in clients:
                # Status distribution
                status = client.get("status", "unknown")
                status_distribution[status] = status_distribution.get(status, 0) + 1
                
                # Industry distribution (if available)
                industry = client.get("industry", "unknown")
                industry_distribution[industry] = industry_distribution.get(industry, 0) + 1
            
            return {
                "status_distribution": status_distribution,
                "industry_distribution": industry_distribution,
                "total_clients": len(clients)
            }
        except Exception as e:
            logger.error(f"Error getting client distribution: {e}")
            return {}
    
    async def get_recent_activity(self, workspace_id: str, limit: int = 10) -> List[Dict]:
        """Get recent activity items for dashboard.
        
        Args:
            workspace_id: Workspace ID
            limit: Maximum number of activity items to return
            
        Returns:
            List of recent activity items
        """
        try:
            activity_items = []
            
            # Get recent clients (limited)
            recent_clients = await self.get_clients(workspace_id, limit=5)
            for client in recent_clients:
                activity_items.append({
                    "id": client.get("id"),
                    "type": "client",
                    "action": "created",
                    "entity_name": client.get("name", "Unknown Client"),
                    "created_at": client.get("created_at"),
                    "created_by": client.get("created_by")
                })
            
            # Get recent tasks (limited)
            recent_tasks = await self.get_tasks(workspace_id, limit=5)
            for task in recent_tasks:
                activity_items.append({
                    "id": task.get("id"),
                    "type": "task",
                    "action": "created",
                    "entity_name": task.get("title", "Unknown Task"),
                    "created_at": task.get("created_at"),
                    "created_by": task.get("created_by")
                })
            
            # Get recent opportunities (limited)
            recent_opportunities = await self.get_opportunities(workspace_id, limit=5)
            for opportunity in recent_opportunities:
                activity_items.append({
                    "id": opportunity.get("id"),
                    "type": "opportunity",
                    "action": "created",
                    "entity_name": opportunity.get("name", "Unknown Opportunity"),
                    "created_at": opportunity.get("created_at"),
                    "created_by": opportunity.get("created_by")
                })
            
            # Sort by created_at (most recent first) and limit
            activity_items.sort(key=lambda x: x.get("created_at", datetime.min.replace(tzinfo=timezone.utc)), reverse=True)
            return activity_items[:limit]
            
        except Exception as e:
            logger.error(f"Error getting recent activity: {e}")
            return []
    
    # Product operations
    async def get_products(self, workspace_id: str, status: Optional[str] = None,
                          limit: Optional[int] = None) -> List[Dict]:
        """Get products from Firestore.
        
        Args:
            workspace_id: Workspace ID to filter by
            status: Optional status filter
            limit: Optional limit for results
            
        Returns:
            List of product documents
        """
        try:
            query = self.db.collection(self.products_collection)
            query = query.where("workspace_id", "==", workspace_id)

            if status:
                query = query.where("status", "==", status)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting products: {e}")
            return []
    
    async def get_product(self, product_id: str) -> Optional[Dict]:
        """Get a single product by ID.
        
        Args:
            product_id: Product ID
            
        Returns:
            Product document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.products_collection).document(product_id)
            doc = doc_ref.get()
            
            if doc.exists:
                return {"id": doc.id, **doc.to_dict()}
            return None
        except Exception as e:
            logger.error(f"Error getting product {product_id}: {e}")
            return None
    
    async def create_product(self, product_data: ProductCreate, user_id: str,
                            workspace_id: str) -> Optional[Dict]:
        """Create a new product in Firestore.
        
        Args:
            product_data: Product creation data
            user_id: User ID creating the product
            workspace_id: Workspace ID
            
        Returns:
            Created product document if successful, None otherwise
        """
        try:
            product_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            product_dict = product_data.model_dump()
            product_dict.update({
                "id": product_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            doc_ref = self.db.collection(self.products_collection).document(product_id)
            doc_ref.set(product_dict)
            
            return {"id": product_id, **product_dict}
        except Exception as e:
            logger.error(f"Error creating product: {e}")
            return None
    
    async def update_product(self, product_id: str, product_update: ProductUpdate) -> Optional[Dict]:
        """Update a product in Firestore.
        
        Args:
            product_id: Product ID
            product_update: Product update data
            
        Returns:
            Updated product document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.products_collection).document(product_id)
            
            # Check if product exists
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            # Update with new data
            update_data = product_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            doc_ref.update(update_data)
            
            # Get updated product
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating product {product_id}: {e}")
            return None
    
    async def delete_product(self, product_id: str) -> bool:
        """Delete a product from Firestore.
        
        Args:
            product_id: Product ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.products_collection).document(product_id)
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting product {product_id}: {e}")
            return False
    
    # Communication Log operations
    async def get_communications(self, workspace_id: str, contact_id: Optional[str] = None,
                               communication_type: Optional[str] = None, limit: Optional[int] = None) -> List[Dict]:
        """Get communications from Firestore.
        
        Args:
            workspace_id: Workspace ID to filter by
            contact_id: Optional contact ID to filter by
            communication_type: Optional communication type filter
            limit: Optional limit for results
            
        Returns:
            List of communication documents
        """
        try:
            query = self.db.collection(self.communications_collection)
            
            # Filter by workspace
            query = query.where("workspace_id", "==", workspace_id)
            
            # Filter by contact if provided
            if contact_id:
                query = query.where("contact_id", "==", contact_id)
            
            # Filter by communication type if provided
            if communication_type:
                query = query.where("communication_type", "==", communication_type)
            
            # Order by timestamp (most recent first)
            query = query.order_by("timestamp", direction="DESCENDING")
            
            # Apply limit if provided
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting communications: {e}")
            return []
    
    async def get_communication(self, communication_id: str, workspace_id: str) -> Optional[Dict]:
        """Get a single communication by ID.
        
        Args:
            communication_id: Communication ID
            workspace_id: Workspace ID for security
            
        Returns:
            Communication document if found, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.communications_collection).document(communication_id)
            doc = doc_ref.get()
            
            if doc.exists:
                communication_data = {"id": doc.id, **doc.to_dict()}
                # Verify workspace access
                if communication_data.get("workspace_id") == workspace_id:
                    return communication_data
            return None
        except Exception as e:
            logger.error(f"Error getting communication {communication_id}: {e}")
            return None
    
    async def create_communication(self, communication_data: CommunicationLogCreate,
                                 user_id: str, workspace_id: str) -> Optional[Dict]:
        """Create a new communication in Firestore.
        
        Args:
            communication_data: Communication creation data
            user_id: User ID creating the communication
            workspace_id: Workspace ID
            
        Returns:
            Created communication document if successful, None otherwise
        """
        try:
            communication_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            communication_dict = communication_data.model_dump()
            communication_dict.update({
                "id": communication_id,
                "user_id": user_id,
                "workspace_id": workspace_id,
                "timestamp": now,
                "created_at": now,
                "updated_at": now
            })
            
            # Validate contact exists and belongs to workspace
            contact = await self.get_client(communication_dict["contact_id"])
            if not contact or contact.get("workspace_id") != workspace_id:
                logger.error(f"Contact {communication_dict['contact_id']} not found or doesn't belong to workspace")
                return None
            
            # Validate company if provided
            if communication_dict.get("company_id"):
                company = await self.get_company(communication_dict["company_id"], workspace_id)
                if not company:
                    logger.error(f"Company {communication_dict['company_id']} not found")
                    return None
            
            doc_ref = self.db.collection(self.communications_collection).document(communication_id)
            doc_ref.set(communication_dict)
            
            # Create follow-up reminder if specified
            if communication_dict.get("follow_up_date"):
                await self._create_follow_up_reminder(
                    communication_id,
                    communication_dict["contact_id"],
                    communication_dict["follow_up_date"],
                    user_id,
                    workspace_id,
                    f"Follow up on: {communication_dict['subject']}"
                )
            
            # Update relationship strength if this communication affects it
            await self._update_relationship_strength_from_communication(
                communication_dict["contact_id"],
                user_id,
                workspace_id,
                now
            )
            
            return {"id": communication_id, **communication_dict}
        except Exception as e:
            logger.error(f"Error creating communication: {e}")
            return None
    
    async def update_communication(self, communication_id: str, 
                                 communication_update: CommunicationLogUpdate,
                                 workspace_id: str) -> Optional[Dict]:
        """Update a communication in Firestore.
        
        Args:
            communication_id: Communication ID
            communication_update: Communication update data
            workspace_id: Workspace ID for security
            
        Returns:
            Updated communication document if successful, None otherwise
        """
        try:
            doc_ref = self.db.collection(self.communications_collection).document(communication_id)
            
            # Check if communication exists and belongs to workspace
            doc = doc_ref.get()
            if not doc.exists:
                return None
            
            existing_data = doc.to_dict()
            if existing_data.get("workspace_id") != workspace_id:
                return None
            
            # Update with new data
            update_data = communication_update.model_dump(exclude_unset=True)
            update_data["updated_at"] = datetime.now(timezone.utc)
            
            doc_ref.update(update_data)
            
            # Get updated communication
            updated_doc = doc_ref.get()
            return {"id": updated_doc.id, **updated_doc.to_dict()}
        except Exception as e:
            logger.error(f"Error updating communication {communication_id}: {e}")
            return None
    
    async def delete_communication(self, communication_id: str, workspace_id: str) -> bool:
        """Delete a communication from Firestore.
        
        Args:
            communication_id: Communication ID
            workspace_id: Workspace ID for security
            
        Returns:
            True if successful, False otherwise
        """
        try:
            doc_ref = self.db.collection(self.communications_collection).document(communication_id)
            
            # Check if communication exists and belongs to workspace
            doc = doc_ref.get()
            if not doc.exists:
                return False
            
            existing_data = doc.to_dict()
            if existing_data.get("workspace_id") != workspace_id:
                return False
            
            doc_ref.delete()
            return True
        except Exception as e:
            logger.error(f"Error deleting communication {communication_id}: {e}")
            return False
    
    async def get_communication_timeline(self, workspace_id: str, contact_id: Optional[str] = None,
                                       date_from: Optional[datetime] = None, date_to: Optional[datetime] = None,
                                       limit: Optional[int] = None) -> List[Dict]:
        """Get communication timeline with enhanced data for display.
        
        Args:
            workspace_id: Workspace ID
            contact_id: Optional contact ID filter
            date_from: Optional start date filter
            date_to: Optional end date filter
            limit: Optional limit for results
            
        Returns:
            List of communication timeline items with enhanced data
        """
        try:
            query = self.db.collection(self.communications_collection)
            query = query.where("workspace_id", "==", workspace_id)
            
            if contact_id:
                query = query.where("contact_id", "==", contact_id)
            
            if date_from:
                query = query.where("timestamp", ">=", date_from)
            
            if date_to:
                query = query.where("timestamp", "<=", date_to)
            
            query = query.order_by("timestamp", direction="DESCENDING")
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            communications = [{"id": doc.id, **doc.to_dict()} for doc in docs]
            
            # Enhance with contact and company information
            enhanced_timeline = []
            for comm in communications:
                # Get contact details
                contact = await self.get_client(comm["contact_id"])
                contact_name = contact.get("name", "Unknown Contact") if contact else "Unknown Contact"
                
                # Get company details if present
                company_name = None
                if comm.get("company_id"):
                    company = await self.get_company(comm["company_id"], workspace_id)
                    company_name = company.get("name") if company else None
                
                # Get user details
                user_name = "Unknown User"  # Would need user lookup service
                
                timeline_item = {
                    "id": comm["id"],
                    "contact_id": comm["contact_id"],
                    "contact_name": contact_name,
                    "company_id": comm.get("company_id"),
                    "company_name": company_name,
                    "communication_type": comm["communication_type"],
                    "direction": comm["direction"],
                    "subject": comm["subject"],
                    "content": comm.get("content"),
                    "outcome": comm.get("outcome"),
                    "timestamp": comm["timestamp"],
                    "user_name": user_name,
                    "duration_minutes": comm.get("duration_minutes"),
                    "follow_up_date": comm.get("follow_up_date"),
                    "follow_up_completed": comm.get("follow_up_completed", False),
                    "attachments": comm.get("attachments", []),
                    "tags": comm.get("tags", []),
                    "priority": comm.get("priority", "normal"),
                    "status": comm.get("status", "completed"),
                    "sentiment": comm.get("sentiment")
                }
                enhanced_timeline.append(timeline_item)
            
            return enhanced_timeline
        except Exception as e:
            logger.error(f"Error getting communication timeline: {e}")
            return []
    
    # Communication Template operations
    async def get_communication_templates(self, workspace_id: str, template_type: Optional[str] = None,
                                        category: Optional[str] = None, limit: Optional[int] = None) -> List[Dict]:
        """Get communication templates from Firestore."""
        try:
            query = self.db.collection(self.communication_templates_collection)
            query = query.where("workspace_id", "==", workspace_id)
            query = query.where("is_active", "==", True)
            
            if template_type:
                query = query.where("template_type", "==", template_type)
            
            if category:
                query = query.where("category", "==", category)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting communication templates: {e}")
            return []
    
    async def create_communication_template(self, template_data: CommunicationTemplateCreate,
                                          user_id: str, workspace_id: str) -> Optional[Dict]:
        """Create a new communication template in Firestore."""
        try:
            template_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            template_dict = template_data.model_dump()
            template_dict.update({
                "id": template_id,
                "created_by": user_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now,
                "usage_count": 0
            })
            
            doc_ref = self.db.collection(self.communication_templates_collection).document(template_id)
            doc_ref.set(template_dict)
            
            return {"id": template_id, **template_dict}
        except Exception as e:
            logger.error(f"Error creating communication template: {e}")
            return None
    
    # Follow-up Reminder operations
    async def get_follow_up_reminders(self, workspace_id: str, assigned_to: Optional[str] = None,
                                    status: Optional[str] = None, overdue_only: bool = False,
                                    limit: Optional[int] = None) -> List[Dict]:
        """Get follow-up reminders from Firestore."""
        try:
            query = self.db.collection(self.follow_up_reminders_collection)
            query = query.where("workspace_id", "==", workspace_id)
            
            if assigned_to:
                query = query.where("assigned_to", "==", assigned_to)
            
            if status:
                query = query.where("status", "==", status)
            
            if overdue_only:
                now = datetime.now(timezone.utc)
                query = query.where("scheduled_for", "<", now)
                query = query.where("status", "==", "pending")
            
            query = query.order_by("scheduled_for")
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error getting follow-up reminders: {e}")
            return []
    
    async def _create_follow_up_reminder(self, communication_id: str, contact_id: str,
                                       scheduled_for: datetime, assigned_to: str,
                                       workspace_id: str, title: str) -> Optional[Dict]:
        """Create a follow-up reminder."""
        try:
            reminder_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            reminder_dict = {
                "id": reminder_id,
                "communication_id": communication_id,
                "contact_id": contact_id,
                "assigned_to": assigned_to,
                "title": title,
                "scheduled_for": scheduled_for,
                "status": "pending",
                "priority": "normal",
                "reminder_sent": False,
                "workspace_id": workspace_id,
                "created_by": assigned_to,
                "created_at": now,
                "updated_at": now
            }
            
            doc_ref = self.db.collection(self.follow_up_reminders_collection).document(reminder_id)
            doc_ref.set(reminder_dict)
            
            return {"id": reminder_id, **reminder_dict}
        except Exception as e:
            logger.error(f"Error creating follow-up reminder: {e}")
            return None
    
    async def _update_relationship_strength_from_communication(self, contact_id: str, user_id: str,
                                                            workspace_id: str, timestamp: datetime):
        """Update relationship strength based on communication activity."""
        try:
            # This is a simplified implementation - would be enhanced with the communication tracking data
            # Get recent communications for this contact
            recent_communications = await self.get_communications(
                workspace_id=workspace_id,
                contact_id=contact_id,
                limit=10
            )
            
            # Calculate new interaction frequency and strength
            communication_count = len(recent_communications)
            interaction_frequency = min(communication_count / 10.0, 1.0)  # Normalize to 0-1
            
            # Update any existing relationships for this contact
            relationships = await self.get_contact_relationships(workspace_id, contact_id)
            for rel in relationships:
                # Simple strength update - would be more sophisticated in production
                new_strength = min((rel.get("relationship_strength", 0.0) + 0.1), 1.0)
                await self.update_contact_relationship(
                    rel["id"],
                    {"relationship_strength": new_strength},
                    workspace_id
                )
        except Exception as e:
            logger.error(f"Error updating relationship strength: {e}")
    
    async def get_communication_analytics(self, workspace_id: str, 
                                        date_from: Optional[datetime] = None,
                                        date_to: Optional[datetime] = None) -> Dict:
        """Get communication analytics for workspace."""
        try:
            query = self.db.collection(self.communications_collection)
            query = query.where("workspace_id", "==", workspace_id)
            
            if date_from:
                query = query.where("timestamp", ">=", date_from)
            
            if date_to:
                query = query.where("timestamp", "<=", date_to)
            
            docs = query.stream()
            communications = [doc.to_dict() for doc in docs]
            
            # Calculate analytics
            total_communications = len(communications)
            
            # Response time analysis
            response_times = [
                comm.get("response_time_minutes", 0) 
                for comm in communications 
                if comm.get("response_time_minutes") is not None
            ]
            avg_response_time = sum(response_times) / len(response_times) if response_times else None
            
            # Communication type distribution
            type_counts = {}
            direction_counts = {}
            outcome_counts = {}
            sentiment_counts = {}
            
            for comm in communications:
                comm_type = comm.get("communication_type", "unknown")
                type_counts[comm_type] = type_counts.get(comm_type, 0) + 1
                
                direction = comm.get("direction", "unknown")
                direction_counts[direction] = direction_counts.get(direction, 0) + 1
                
                outcome = comm.get("outcome", "unknown")
                outcome_counts[outcome] = outcome_counts.get(outcome, 0) + 1
                
                sentiment = comm.get("sentiment", "neutral")
                sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
            
            # Get follow-up counts
            pending_follow_ups = await self.get_follow_up_reminders(
                workspace_id, status="pending", limit=1000
            )
            overdue_follow_ups = await self.get_follow_up_reminders(
                workspace_id, overdue_only=True, limit=1000
            )
            
            return {
                "workspace_id": workspace_id,
                "total_communications": total_communications,
                "avg_response_time_minutes": avg_response_time,
                "communication_types": type_counts,
                "communication_directions": direction_counts,
                "outcomes": outcome_counts,
                "sentiment_distribution": sentiment_counts,
                "pending_follow_ups": len(pending_follow_ups),
                "overdue_follow_ups": len(overdue_follow_ups)
            }
        except Exception as e:
            logger.error(f"Error getting communication analytics: {e}")
            return {}
    
    async def get_relationship_strength_trend(self, contact_id: str, related_contact_id: str,
                                            workspace_id: str, days_back: int = 90) -> List[Dict]:
        """Get relationship strength trend analysis over time.
        
        Args:
            contact_id: Primary contact ID
            related_contact_id: Related contact ID
            workspace_id: Workspace ID
            days_back: Number of days to analyze (default 90)
            
        Returns:
            List of strength trend data points
        """
        try:
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Get communications between these contacts
            communications = await self.get_communications(
                workspace_id=workspace_id,
                contact_id=contact_id
            )
            
            # Filter communications with the specific related contact
            filtered_comms = [
                comm for comm in communications
                if comm.get("related_contact_id") == related_contact_id or 
                   (comm.get("participants") and related_contact_id in comm.get("participants", []))
            ]
            
            # Group by week and calculate strength trends
            trend_data = []
            current_date = start_date
            
            while current_date <= end_date:
                week_end = current_date + timedelta(days=7)
                
                # Get communications in this week
                week_comms = [
                    comm for comm in filtered_comms
                    if current_date <= comm.get("timestamp", datetime.min.replace(tzinfo=timezone.utc)) < week_end
                ]
                
                # Calculate strength metrics for this week
                interaction_count = len(week_comms)
                avg_sentiment = self._calculate_average_sentiment(week_comms)
                response_rate = self._calculate_response_rate(week_comms)
                
                # Calculate composite strength score
                base_strength = min(interaction_count / 5.0, 1.0)  # Normalize to max 5 interactions
                sentiment_modifier = (avg_sentiment + 1.0) / 2.0  # Convert -1,1 to 0,1
                response_modifier = response_rate
                
                week_strength = min(base_strength * 0.5 + sentiment_modifier * 0.3 + response_modifier * 0.2, 1.0)
                
                trend_data.append({
                    "date": current_date.isoformat(),
                    "week_start": current_date.isoformat(),
                    "week_end": week_end.isoformat(),
                    "strength_score": round(week_strength, 3),
                    "interaction_count": interaction_count,
                    "avg_sentiment": round(avg_sentiment, 3),
                    "response_rate": round(response_rate, 3),
                    "trend_direction": "stable"  # Will be calculated below
                })
                
                current_date = week_end
            
            # Calculate trend directions
            for i in range(1, len(trend_data)):
                current_strength = trend_data[i]["strength_score"]
                previous_strength = trend_data[i-1]["strength_score"]
                
                if current_strength > previous_strength + 0.05:
                    trend_data[i]["trend_direction"] = "improving"
                elif current_strength < previous_strength - 0.05:
                    trend_data[i]["trend_direction"] = "declining"
                else:
                    trend_data[i]["trend_direction"] = "stable"
            
            return trend_data
            
        except Exception as e:
            logger.error(f"Error getting relationship strength trend: {e}")
            return []
    
    async def get_relationship_health_indicators(self, contact_id: str, related_contact_id: str,
                                               workspace_id: str) -> Dict:
        """Get comprehensive relationship health indicators.
        
        Args:
            contact_id: Primary contact ID
            related_contact_id: Related contact ID
            workspace_id: Workspace ID
            
        Returns:
            Dictionary with health indicators and recommendations
        """
        try:
            # Get relationship data
            relationship = None
            relationships = await self.get_contact_relationships(workspace_id, contact_id)
            for rel in relationships:
                if (rel.get("contact_id") == contact_id and rel.get("related_contact_id") == related_contact_id) or \
                   (rel.get("contact_id") == related_contact_id and rel.get("related_contact_id") == contact_id):
                    relationship = rel
                    break
            
            if not relationship:
                return {"error": "Relationship not found"}
            
            # Get recent communications (last 90 days)
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=90)
            
            communications = await self.get_communications(
                workspace_id=workspace_id,
                contact_id=contact_id
            )
            
            recent_comms = [
                comm for comm in communications
                if comm.get("timestamp", datetime.min.replace(tzinfo=timezone.utc)) >= start_date and
                   (comm.get("related_contact_id") == related_contact_id or
                    (comm.get("participants") and related_contact_id in comm.get("participants", [])))
            ]
            
            # Calculate health indicators
            health_indicators = {}
            
            # 1. Communication Frequency Health
            comm_frequency = len(recent_comms) / 13  # Per week over 13 weeks
            if comm_frequency >= 2:
                freq_health = "excellent"
                freq_score = 1.0
            elif comm_frequency >= 1:
                freq_health = "good"
                freq_score = 0.8
            elif comm_frequency >= 0.5:
                freq_health = "fair"
                freq_score = 0.6
            else:
                freq_health = "poor"
                freq_score = 0.3
            
            health_indicators["communication_frequency"] = {
                "health_status": freq_health,
                "score": freq_score,
                "weekly_average": round(comm_frequency, 2),
                "total_interactions": len(recent_comms)
            }
            
            # 2. Response Time Health
            response_times = [
                comm.get("response_time_minutes", 0)
                for comm in recent_comms
                if comm.get("response_time_minutes") is not None and comm.get("response_time_minutes") > 0
            ]
            
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
                if avg_response_time <= 60:  # 1 hour
                    response_health = "excellent"
                    response_score = 1.0
                elif avg_response_time <= 240:  # 4 hours
                    response_health = "good"
                    response_score = 0.8
                elif avg_response_time <= 1440:  # 24 hours
                    response_health = "fair"
                    response_score = 0.6
                else:
                    response_health = "poor"
                    response_score = 0.3
            else:
                response_health = "unknown"
                response_score = 0.5
                avg_response_time = 0
            
            health_indicators["response_time"] = {
                "health_status": response_health,
                "score": response_score,
                "avg_response_minutes": round(avg_response_time, 1),
                "sample_size": len(response_times)
            }
            
            # 3. Sentiment Health
            sentiments = [
                comm.get("sentiment", "neutral")
                for comm in recent_comms
                if comm.get("sentiment")
            ]
            
            sentiment_scores = []
            for sentiment in sentiments:
                if sentiment == "very_positive":
                    sentiment_scores.append(1.0)
                elif sentiment == "positive":
                    sentiment_scores.append(0.5)
                elif sentiment == "neutral":
                    sentiment_scores.append(0.0)
                elif sentiment == "negative":
                    sentiment_scores.append(-0.5)
                elif sentiment == "very_negative":
                    sentiment_scores.append(-1.0)
            
            if sentiment_scores:
                avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                if avg_sentiment >= 0.3:
                    sentiment_health = "excellent"
                    sentiment_score = 1.0
                elif avg_sentiment >= 0.1:
                    sentiment_health = "good"
                    sentiment_score = 0.8
                elif avg_sentiment >= -0.1:
                    sentiment_health = "fair"
                    sentiment_score = 0.6
                else:
                    sentiment_health = "poor"
                    sentiment_score = 0.3
            else:
                sentiment_health = "unknown"
                sentiment_score = 0.5
                avg_sentiment = 0.0
            
            health_indicators["sentiment"] = {
                "health_status": sentiment_health,
                "score": sentiment_score,
                "avg_sentiment": round(avg_sentiment, 3),
                "positive_interactions": len([s for s in sentiment_scores if s > 0]),
                "negative_interactions": len([s for s in sentiment_scores if s < 0])
            }
            
            # 4. Engagement Consistency
            # Check for gaps in communication
            if len(recent_comms) >= 2:
                comm_dates = sorted([
                    comm.get("timestamp", datetime.min.replace(tzinfo=timezone.utc))
                    for comm in recent_comms
                ])
                
                gaps = []
                for i in range(1, len(comm_dates)):
                    gap_days = (comm_dates[i] - comm_dates[i-1]).days
                    gaps.append(gap_days)
                
                max_gap = max(gaps) if gaps else 0
                avg_gap = sum(gaps) / len(gaps) if gaps else 0
                
                if max_gap <= 7:
                    consistency_health = "excellent"
                    consistency_score = 1.0
                elif max_gap <= 14:
                    consistency_health = "good"
                    consistency_score = 0.8
                elif max_gap <= 30:
                    consistency_health = "fair"
                    consistency_score = 0.6
                else:
                    consistency_health = "poor"
                    consistency_score = 0.3
            else:
                consistency_health = "insufficient_data"
                consistency_score = 0.5
                max_gap = 0
                avg_gap = 0
            
            health_indicators["engagement_consistency"] = {
                "health_status": consistency_health,
                "score": consistency_score,
                "max_gap_days": max_gap,
                "avg_gap_days": round(avg_gap, 1)
            }
            
            # 5. Overall Relationship Health
            scores = [
                health_indicators["communication_frequency"]["score"],
                health_indicators["response_time"]["score"],
                health_indicators["sentiment"]["score"],
                health_indicators["engagement_consistency"]["score"]
            ]
            
            overall_score = sum(scores) / len(scores)
            
            if overall_score >= 0.8:
                overall_health = "excellent"
            elif overall_score >= 0.65:
                overall_health = "good"
            elif overall_score >= 0.5:
                overall_health = "fair"
            else:
                overall_health = "needs_attention"
            
            # Generate recommendations
            recommendations = []
            
            if health_indicators["communication_frequency"]["score"] < 0.6:
                recommendations.append("Increase communication frequency - aim for at least weekly contact")
            
            if health_indicators["response_time"]["score"] < 0.6:
                recommendations.append("Improve response times - try to respond within 4 hours")
            
            if health_indicators["sentiment"]["score"] < 0.6:
                recommendations.append("Focus on positive interactions - address any concerns promptly")
            
            if health_indicators["engagement_consistency"]["score"] < 0.6:
                recommendations.append("Maintain regular contact - avoid gaps longer than 2 weeks")
            
            if overall_score >= 0.8:
                recommendations.append("Excellent relationship health - maintain current engagement level")
            elif not recommendations:
                recommendations.append("Good relationship health - continue current practices")
            
            return {
                "contact_id": contact_id,
                "related_contact_id": related_contact_id,
                "workspace_id": workspace_id,
                "analysis_date": datetime.now(timezone.utc).isoformat(),
                "analysis_period_days": 90,
                "overall_health": {
                    "status": overall_health,
                    "score": round(overall_score, 3)
                },
                "health_indicators": health_indicators,
                "recommendations": recommendations,
                "relationship_strength": relationship.get("relationship_strength", 0.0),
                "last_interaction": max([comm.get("timestamp") for comm in recent_comms]) if recent_comms else None
            }
            
        except Exception as e:
            logger.error(f"Error getting relationship health indicators: {e}")
            return {"error": str(e)}
    
    def _calculate_average_sentiment(self, communications: List[Dict]) -> float:
        """Calculate average sentiment from communications."""
        if not communications:
            return 0.0
        
        sentiment_scores = []
        for comm in communications:
            sentiment = comm.get("sentiment", "neutral")
            if sentiment == "very_positive":
                sentiment_scores.append(1.0)
            elif sentiment == "positive":
                sentiment_scores.append(0.5)
            elif sentiment == "neutral":
                sentiment_scores.append(0.0)
            elif sentiment == "negative":
                sentiment_scores.append(-0.5)
            elif sentiment == "very_negative":
                sentiment_scores.append(-1.0)
        
        return sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0
    
    def _calculate_response_rate(self, communications: List[Dict]) -> float:
        """Calculate response rate from communications."""
        if not communications:
            return 0.0
        
        outbound_comms = [
            comm for comm in communications
            if comm.get("direction") == "outbound"
        ]
        
        if not outbound_comms:
            return 1.0  # No outbound communications to respond to
        
        responded_comms = [
            comm for comm in outbound_comms
            if comm.get("response_received", False)
        ]
        
        return len(responded_comms) / len(outbound_comms)
    
    # Email Integration Support Methods
    async def search_clients_by_email(self, email_address: str, workspace_id: str) -> List[Dict]:
        """Search for clients by email address."""
        try:
            query = self.db.collection(self.clients_collection)
            query = query.where("workspace_id", "==", workspace_id)
            query = query.where("email", "==", email_address.lower())
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error searching clients by email: {e}")
            return []
    
    async def search_communications_by_external_id(self, external_id: str, workspace_id: str) -> List[Dict]:
        """Search for communications by external ID (e.g., email message ID)."""
        try:
            query = self.db.collection(self.communications_collection)
            query = query.where("workspace_id", "==", workspace_id)
            query = query.where("external_id", "==", external_id)
            
            docs = query.stream()
            return [{"id": doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            logger.error(f"Error searching communications by external ID: {e}")
            return []
    
    async def create_communication_log(self, communication_data: Dict, workspace_id: str) -> Optional[Dict]:
        """Create communication log from email integration data."""
        try:
            communication_id = str(uuid4())
            now = datetime.now(timezone.utc)
            
            # Add required fields
            communication_data.update({
                "id": communication_id,
                "workspace_id": workspace_id,
                "created_at": now,
                "updated_at": now
            })
            
            # Use provided timestamp or current time
            if "timestamp" not in communication_data:
                communication_data["timestamp"] = now
            
            doc_ref = self.db.collection(self.communications_collection).document(communication_id)
            doc_ref.set(communication_data)
            
            return {"id": communication_id, **communication_data}
        except Exception as e:
            logger.error(f"Error creating communication log: {e}")
            return None
    
    # Recurring Follow-up Support Methods
    async def create_recurring_follow_up(self, base_reminder_data: Dict, recurrence_pattern: Dict,
                                       user_id: str, workspace_id: str) -> List[Dict]:
        """Create recurring follow-up reminders based on pattern."""
        try:
            pattern_type = recurrence_pattern.get("type", "none")  # daily, weekly, monthly, yearly
            frequency = recurrence_pattern.get("frequency", 1)  # every N periods
            end_date = recurrence_pattern.get("end_date")
            max_occurrences = recurrence_pattern.get("max_occurrences", 10)
            
            if pattern_type == "none":
                # Single follow-up
                return [await self._create_follow_up_reminder(
                    base_reminder_data.get("communication_id"),
                    base_reminder_data.get("contact_id"),
                    base_reminder_data.get("scheduled_for"),
                    user_id,
                    workspace_id,
                    base_reminder_data.get("title", "Follow-up reminder")
                )]
            
            created_reminders = []
            current_date = base_reminder_data.get("scheduled_for")
            
            for occurrence in range(max_occurrences):
                if end_date and current_date > end_date:
                    break
                
                # Create reminder for this occurrence
                reminder_title = f"{base_reminder_data.get('title', 'Follow-up')} (#{occurrence + 1})"
                reminder = await self._create_follow_up_reminder(
                    base_reminder_data.get("communication_id"),
                    base_reminder_data.get("contact_id"),
                    current_date,
                    user_id,
                    workspace_id,
                    reminder_title
                )
                
                if reminder:
                    # Add recurrence metadata
                    reminder["recurrence_pattern"] = recurrence_pattern
                    reminder["occurrence_number"] = occurrence + 1
                    created_reminders.append(reminder)
                
                # Calculate next occurrence date
                if pattern_type == "daily":
                    current_date = current_date + timedelta(days=frequency)
                elif pattern_type == "weekly":
                    current_date = current_date + timedelta(weeks=frequency)
                elif pattern_type == "monthly":
                    # Add months (approximate with 30 days for simplicity)
                    current_date = current_date + timedelta(days=30 * frequency)
                elif pattern_type == "yearly":
                    # Add years (approximate with 365 days)
                    current_date = current_date + timedelta(days=365 * frequency)
            
            return created_reminders
        except Exception as e:
            logger.error(f"Error creating recurring follow-up: {e}")
            return []
    
    async def get_follow_up_analytics(self, workspace_id: str, user_id: Optional[str] = None,
                                    date_from: Optional[datetime] = None,
                                    date_to: Optional[datetime] = None) -> Dict:
        """Get comprehensive follow-up analytics."""
        try:
            query = self.db.collection(self.follow_up_reminders_collection)
            query = query.where("workspace_id", "==", workspace_id)
            
            if user_id:
                query = query.where("assigned_to", "==", user_id)
            
            if date_from:
                query = query.where("scheduled_for", ">=", date_from)
                
            if date_to:
                query = query.where("scheduled_for", "<=", date_to)
            
            docs = query.stream()
            follow_ups = [{"id": doc.id, **doc.to_dict()} for doc in docs]
            
            # Calculate analytics
            analytics = {
                "total_follow_ups": len(follow_ups),
                "by_status": {},
                "by_priority": {},
                "completion_rate": 0.0,
                "overdue_count": 0,
                "avg_completion_time_hours": 0.0,
                "user_performance": {},
                "trends": {
                    "daily_created": {},
                    "daily_completed": {},
                    "weekly_performance": {}
                }
            }
            
            completed_follow_ups = []
            overdue_count = 0
            now = datetime.now(timezone.utc)
            
            for follow_up in follow_ups:
                # Status distribution
                status = follow_up.get("status", "pending")
                analytics["by_status"][status] = analytics["by_status"].get(status, 0) + 1
                
                # Priority distribution
                priority = follow_up.get("priority", "normal")
                analytics["by_priority"][priority] = analytics["by_priority"].get(priority, 0) + 1
                
                # Track completed follow-ups
                if status == "completed":
                    completed_follow_ups.append(follow_up)
                
                # Count overdue
                scheduled_for = follow_up.get("scheduled_for")
                if status == "pending" and scheduled_for and scheduled_for < now:
                    overdue_count += 1
                
                # User performance tracking
                assigned_to = follow_up.get("assigned_to", "unknown")
                if assigned_to not in analytics["user_performance"]:
                    analytics["user_performance"][assigned_to] = {
                        "total": 0, "completed": 0, "overdue": 0, "completion_rate": 0.0
                    }
                
                analytics["user_performance"][assigned_to]["total"] += 1
                if status == "completed":
                    analytics["user_performance"][assigned_to]["completed"] += 1
                elif status == "pending" and scheduled_for and scheduled_for < now:
                    analytics["user_performance"][assigned_to]["overdue"] += 1
            
            # Calculate completion rate
            if follow_ups:
                analytics["completion_rate"] = len(completed_follow_ups) / len(follow_ups) * 100
            
            analytics["overdue_count"] = overdue_count
            
            # Calculate average completion time
            completion_times = []
            for follow_up in completed_follow_ups:
                created_at = follow_up.get("created_at")
                completed_at = follow_up.get("completed_at")
                if created_at and completed_at:
                    completion_time = (completed_at - created_at).total_seconds() / 3600  # hours
                    completion_times.append(completion_time)
            
            if completion_times:
                analytics["avg_completion_time_hours"] = sum(completion_times) / len(completion_times)
            
            # Calculate user completion rates
            for user_data in analytics["user_performance"].values():
                if user_data["total"] > 0:
                    user_data["completion_rate"] = (user_data["completed"] / user_data["total"]) * 100
            
            # Generate trend data
            analytics["trends"] = await self._calculate_follow_up_trends(follow_ups)
            
            return analytics
        except Exception as e:
            logger.error(f"Error getting follow-up analytics: {e}")
            return {}
    
    async def _calculate_follow_up_trends(self, follow_ups: List[Dict]) -> Dict:
        """Calculate trend data for follow-ups."""
        try:
            trends = {
                "daily_created": {},
                "daily_completed": {},
                "weekly_performance": {}
            }
            
            # Daily creation trends
            for follow_up in follow_ups:
                created_at = follow_up.get("created_at")
                if created_at:
                    date_key = created_at.strftime("%Y-%m-%d")
                    trends["daily_created"][date_key] = trends["daily_created"].get(date_key, 0) + 1
            
            # Daily completion trends
            for follow_up in follow_ups:
                if follow_up.get("status") == "completed":
                    completed_at = follow_up.get("completed_at")
                    if completed_at:
                        date_key = completed_at.strftime("%Y-%m-%d")
                        trends["daily_completed"][date_key] = trends["daily_completed"].get(date_key, 0) + 1
            
            # Weekly performance trends
            weekly_data = {}
            for follow_up in follow_ups:
                created_at = follow_up.get("created_at")
                if created_at:
                    # Get Monday of the week
                    monday = created_at - timedelta(days=created_at.weekday())
                    week_key = monday.strftime("%Y-%m-%d")
                    
                    if week_key not in weekly_data:
                        weekly_data[week_key] = {"created": 0, "completed": 0, "completion_rate": 0.0}
                    
                    weekly_data[week_key]["created"] += 1
                    if follow_up.get("status") == "completed":
                        weekly_data[week_key]["completed"] += 1
            
            # Calculate weekly completion rates
            for week_data in weekly_data.values():
                if week_data["created"] > 0:
                    week_data["completion_rate"] = (week_data["completed"] / week_data["created"]) * 100
            
            trends["weekly_performance"] = weekly_data
            
            return trends
        except Exception as e:
            logger.error(f"Error calculating follow-up trends: {e}")
            return {}
    
    async def update_follow_up_status(self, reminder_id: str, status: str, 
                                    completion_notes: Optional[str] = None) -> bool:
        """Update follow-up reminder status."""
        try:
            doc_ref = self.db.collection(self.follow_up_reminders_collection).document(reminder_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return False
            
            update_data = {
                "status": status,
                "updated_at": datetime.now(timezone.utc)
            }
            
            if status == "completed":
                update_data["completed_at"] = datetime.now(timezone.utc)
                if completion_notes:
                    update_data["completion_notes"] = completion_notes
            
            doc_ref.update(update_data)
            return True
        except Exception as e:
            logger.error(f"Error updating follow-up status: {e}")
            return False
    
    # Performance Dashboard Methods
    async def get_communication_performance_dashboard(self, workspace_id: str, 
                                                    date_from: Optional[datetime] = None,
                                                    date_to: Optional[datetime] = None) -> Dict:
        """Get comprehensive communication performance dashboard data."""
        try:
            if not date_from:
                date_from = datetime.now(timezone.utc) - timedelta(days=30)
            if not date_to:
                date_to = datetime.now(timezone.utc)
            
            # Get all communications in date range
            communications = await self.get_communications(workspace_id)
            filtered_comms = [
                comm for comm in communications
                if date_from <= comm.get("timestamp", datetime.min.replace(tzinfo=timezone.utc)) <= date_to
            ]
            
            # Get follow-up analytics
            follow_up_analytics = await self.get_follow_up_analytics(
                workspace_id, date_from=date_from, date_to=date_to
            )
            
            # Calculate response time benchmarks
            response_time_benchmarks = await self._calculate_response_time_benchmarks(filtered_comms)
            
            # Calculate engagement metrics
            engagement_metrics = await self._calculate_engagement_metrics(filtered_comms, workspace_id)
            
            # Get user performance comparison
            user_performance = await self._get_user_performance_comparison(filtered_comms, workspace_id)
            
            # Calculate communication effectiveness
            effectiveness_metrics = await self._calculate_communication_effectiveness(filtered_comms)
            
            dashboard = {
                "workspace_id": workspace_id,
                "date_range": {
                    "from": date_from.isoformat(),
                    "to": date_to.isoformat()
                },
                "overview": {
                    "total_communications": len(filtered_comms),
                    "active_contacts": len(set(comm.get("contact_id") for comm in filtered_comms if comm.get("contact_id"))),
                    "avg_daily_communications": len(filtered_comms) / max((date_to - date_from).days, 1),
                    "response_rate": self._calculate_overall_response_rate(filtered_comms)
                },
                "response_time_benchmarks": response_time_benchmarks,
                "engagement_metrics": engagement_metrics,
                "follow_up_performance": follow_up_analytics,
                "user_performance": user_performance,
                "effectiveness_metrics": effectiveness_metrics,
                "trend_analysis": await self._calculate_performance_trends(filtered_comms, date_from, date_to)
            }
            
            return dashboard
        except Exception as e:
            logger.error(f"Error getting communication performance dashboard: {e}")
            return {}
    
    async def _calculate_response_time_benchmarks(self, communications: List[Dict]) -> Dict:
        """Calculate response time benchmarks and percentiles."""
        try:
            response_times = [
                comm.get("response_time_minutes", 0) 
                for comm in communications 
                if comm.get("response_time_minutes") and comm.get("response_time_minutes") > 0
            ]
            
            if not response_times:
                return {}
            
            response_times.sort()
            n = len(response_times)
            
            return {
                "total_responses": n,
                "average_minutes": sum(response_times) / n,
                "median_minutes": response_times[n // 2],
                "percentiles": {
                    "p25": response_times[int(n * 0.25)],
                    "p50": response_times[int(n * 0.50)],
                    "p75": response_times[int(n * 0.75)],
                    "p90": response_times[int(n * 0.90)],
                    "p95": response_times[int(n * 0.95)]
                },
                "benchmarks": {
                    "excellent": "< 60 minutes",
                    "good": "60-240 minutes", 
                    "fair": "240-1440 minutes",
                    "poor": "> 1440 minutes"
                },
                "distribution": {
                    "excellent": len([rt for rt in response_times if rt < 60]),
                    "good": len([rt for rt in response_times if 60 <= rt < 240]),
                    "fair": len([rt for rt in response_times if 240 <= rt < 1440]),
                    "poor": len([rt for rt in response_times if rt >= 1440])
                }
            }
        except Exception as e:
            logger.error(f"Error calculating response time benchmarks: {e}")
            return {}
    
    async def _calculate_engagement_metrics(self, communications: List[Dict], workspace_id: str) -> Dict:
        """Calculate engagement metrics for dashboard."""
        try:
            # Contact engagement analysis
            contact_engagement = {}
            for comm in communications:
                contact_id = comm.get("contact_id")
                if not contact_id:
                    continue
                
                if contact_id not in contact_engagement:
                    contact_engagement[contact_id] = {
                        "total_communications": 0,
                        "inbound": 0,
                        "outbound": 0,
                        "response_rate": 0.0,
                        "avg_sentiment": 0.0,
                        "sentiment_scores": []
                    }
                
                contact_engagement[contact_id]["total_communications"] += 1
                
                if comm.get("direction") == "inbound":
                    contact_engagement[contact_id]["inbound"] += 1
                elif comm.get("direction") == "outbound":
                    contact_engagement[contact_id]["outbound"] += 1
                
                # Sentiment scoring
                sentiment = comm.get("sentiment", "neutral")
                sentiment_score = {
                    "very_positive": 2, "positive": 1, "neutral": 0, 
                    "negative": -1, "very_negative": -2
                }.get(sentiment, 0)
                contact_engagement[contact_id]["sentiment_scores"].append(sentiment_score)
            
            # Calculate averages and response rates
            for contact_data in contact_engagement.values():
                if contact_data["outbound"] > 0:
                    contact_data["response_rate"] = (contact_data["inbound"] / contact_data["outbound"]) * 100
                
                if contact_data["sentiment_scores"]:
                    contact_data["avg_sentiment"] = sum(contact_data["sentiment_scores"]) / len(contact_data["sentiment_scores"])
                
                del contact_data["sentiment_scores"]  # Remove raw data
            
            # Overall engagement metrics
            total_contacts = len(contact_engagement)
            highly_engaged = len([c for c in contact_engagement.values() if c["total_communications"] >= 5])
            avg_communications_per_contact = sum(c["total_communications"] for c in contact_engagement.values()) / max(total_contacts, 1)
            
            return {
                "total_active_contacts": total_contacts,
                "highly_engaged_contacts": highly_engaged,
                "engagement_rate": (highly_engaged / max(total_contacts, 1)) * 100,
                "avg_communications_per_contact": avg_communications_per_contact,
                "contact_details": dict(list(contact_engagement.items())[:10])  # Top 10 for preview
            }
        except Exception as e:
            logger.error(f"Error calculating engagement metrics: {e}")
            return {}
    
    async def _get_user_performance_comparison(self, communications: List[Dict], workspace_id: str) -> Dict:
        """Get user performance comparison for dashboard."""
        try:
            user_stats = {}
            
            for comm in communications:
                user_id = comm.get("user_id", "unknown")
                if user_id not in user_stats:
                    user_stats[user_id] = {
                        "total_communications": 0,
                        "response_times": [],
                        "sentiment_scores": [],
                        "follow_ups_created": 0,
                        "by_type": {},
                        "by_direction": {}
                    }
                
                stats = user_stats[user_id]
                stats["total_communications"] += 1
                
                # Response times
                if comm.get("response_time_minutes"):
                    stats["response_times"].append(comm["response_time_minutes"])
                
                # Sentiment tracking
                sentiment = comm.get("sentiment", "neutral")
                sentiment_score = {
                    "very_positive": 2, "positive": 1, "neutral": 0, 
                    "negative": -1, "very_negative": -2
                }.get(sentiment, 0)
                stats["sentiment_scores"].append(sentiment_score)
                
                # Communication type distribution
                comm_type = comm.get("communication_type", "unknown")
                stats["by_type"][comm_type] = stats["by_type"].get(comm_type, 0) + 1
                
                # Direction distribution
                direction = comm.get("direction", "unknown")
                stats["by_direction"][direction] = stats["by_direction"].get(direction, 0) + 1
                
                # Count follow-ups created
                if comm.get("follow_up_date"):
                    stats["follow_ups_created"] += 1
            
            # Calculate performance metrics for each user
            user_performance = {}
            for user_id, stats in user_stats.items():
                performance = {
                    "user_id": user_id,
                    "total_communications": stats["total_communications"],
                    "avg_response_time": sum(stats["response_times"]) / len(stats["response_times"]) if stats["response_times"] else None,
                    "avg_sentiment": sum(stats["sentiment_scores"]) / len(stats["sentiment_scores"]) if stats["sentiment_scores"] else 0,
                    "follow_up_rate": (stats["follow_ups_created"] / max(stats["total_communications"], 1)) * 100,
                    "communication_breakdown": stats["by_type"],
                    "direction_breakdown": stats["by_direction"]
                }
                user_performance[user_id] = performance
            
            # Rank users by performance
            sorted_users = sorted(
                user_performance.values(),
                key=lambda x: (x["total_communications"], -x.get("avg_response_time", float('inf')), x["avg_sentiment"]),
                reverse=True
            )
            
            return {
                "total_users": len(user_performance),
                "user_rankings": sorted_users,
                "top_performers": sorted_users[:5],
                "performance_metrics": {
                    "avg_communications_per_user": sum(u["total_communications"] for u in user_performance.values()) / max(len(user_performance), 1),
                    "avg_response_time_all_users": sum(u["avg_response_time"] for u in user_performance.values() if u["avg_response_time"]) / 
                                                 max(len([u for u in user_performance.values() if u["avg_response_time"]]), 1),
                    "avg_sentiment_all_users": sum(u["avg_sentiment"] for u in user_performance.values()) / max(len(user_performance), 1)
                }
            }
        except Exception as e:
            logger.error(f"Error getting user performance comparison: {e}")
            return {}
    
    async def _calculate_communication_effectiveness(self, communications: List[Dict]) -> Dict:
        """Calculate communication effectiveness metrics."""
        try:
            # Outcome analysis
            outcomes = {}
            total_with_outcome = 0
            
            for comm in communications:
                outcome = comm.get("outcome")
                if outcome:
                    outcomes[outcome] = outcomes.get(outcome, 0) + 1
                    total_with_outcome += 1
            
            # Calculate success rates
            successful_outcomes = ["successful", "resolved", "agreed", "closed"]
            success_count = sum(outcomes.get(outcome, 0) for outcome in successful_outcomes)
            success_rate = (success_count / max(total_with_outcome, 1)) * 100
            
            # Channel effectiveness
            channel_performance = {}
            for comm in communications:
                channel = comm.get("channel", "unknown")
                if channel not in channel_performance:
                    channel_performance[channel] = {
                        "total": 0, "successful": 0, "avg_response_time": 0, "response_times": []
                    }
                
                channel_performance[channel]["total"] += 1
                
                if comm.get("outcome") in successful_outcomes:
                    channel_performance[channel]["successful"] += 1
                
                if comm.get("response_time_minutes"):
                    channel_performance[channel]["response_times"].append(comm["response_time_minutes"])
            
            # Calculate channel success rates and avg response times
            for channel_data in channel_performance.values():
                if channel_data["total"] > 0:
                    channel_data["success_rate"] = (channel_data["successful"] / channel_data["total"]) * 100
                
                if channel_data["response_times"]:
                    channel_data["avg_response_time"] = sum(channel_data["response_times"]) / len(channel_data["response_times"])
                
                del channel_data["response_times"]  # Remove raw data
            
            return {
                "overall_success_rate": success_rate,
                "outcome_distribution": outcomes,
                "channel_effectiveness": channel_performance,
                "total_communications_with_outcomes": total_with_outcome,
                "effectiveness_score": min(success_rate, 100)  # Cap at 100%
            }
        except Exception as e:
            logger.error(f"Error calculating communication effectiveness: {e}")
            return {}
    
    async def _calculate_performance_trends(self, communications: List[Dict], 
                                          date_from: datetime, date_to: datetime) -> Dict:
        """Calculate performance trends over time."""
        try:
            # Daily performance tracking
            daily_performance = {}
            current_date = date_from.date()
            end_date = date_to.date()
            
            while current_date <= end_date:
                daily_performance[current_date.isoformat()] = {
                    "date": current_date.isoformat(),
                    "communications": 0,
                    "response_times": [],
                    "sentiment_scores": [],
                    "success_count": 0
                }
                current_date += timedelta(days=1)
            
            # Populate daily data
            successful_outcomes = ["successful", "resolved", "agreed", "closed"]
            
            for comm in communications:
                comm_date = comm.get("timestamp", datetime.min.replace(tzinfo=timezone.utc)).date()
                date_key = comm_date.isoformat()
                
                if date_key in daily_performance:
                    daily_performance[date_key]["communications"] += 1
                    
                    if comm.get("response_time_minutes"):
                        daily_performance[date_key]["response_times"].append(comm["response_time_minutes"])
                    
                    sentiment = comm.get("sentiment", "neutral")
                    sentiment_score = {
                        "very_positive": 2, "positive": 1, "neutral": 0, 
                        "negative": -1, "very_negative": -2
                    }.get(sentiment, 0)
                    daily_performance[date_key]["sentiment_scores"].append(sentiment_score)
                    
                    if comm.get("outcome") in successful_outcomes:
                        daily_performance[date_key]["success_count"] += 1
            
            # Calculate daily averages and rates
            for day_data in daily_performance.values():
                if day_data["response_times"]:
                    day_data["avg_response_time"] = sum(day_data["response_times"]) / len(day_data["response_times"])
                else:
                    day_data["avg_response_time"] = None
                
                if day_data["sentiment_scores"]:
                    day_data["avg_sentiment"] = sum(day_data["sentiment_scores"]) / len(day_data["sentiment_scores"])
                else:
                    day_data["avg_sentiment"] = 0
                
                if day_data["communications"] > 0:
                    day_data["success_rate"] = (day_data["success_count"] / day_data["communications"]) * 100
                else:
                    day_data["success_rate"] = 0
                
                # Remove raw data
                del day_data["response_times"]
                del day_data["sentiment_scores"]
            
            # Calculate trend direction
            performance_values = list(daily_performance.values())
            if len(performance_values) >= 7:
                recent_week = performance_values[-7:]
                previous_week = performance_values[-14:-7] if len(performance_values) >= 14 else performance_values[:-7]
                
                recent_avg_comms = sum(d["communications"] for d in recent_week) / 7
                previous_avg_comms = sum(d["communications"] for d in previous_week) / len(previous_week)
                
                trend_direction = "improving" if recent_avg_comms > previous_avg_comms else "declining" if recent_avg_comms < previous_avg_comms else "stable"
            else:
                trend_direction = "insufficient_data"
            
            return {
                "daily_performance": daily_performance,
                "trend_direction": trend_direction,
                "performance_summary": {
                    "total_days": len(daily_performance),
                    "avg_daily_communications": sum(d["communications"] for d in daily_performance.values()) / len(daily_performance),
                    "best_day": max(daily_performance.values(), key=lambda x: x["communications"])["date"],
                    "avg_success_rate": sum(d["success_rate"] for d in daily_performance.values()) / len(daily_performance)
                }
            }
        except Exception as e:
            logger.error(f"Error calculating performance trends: {e}")
            return {}
    
    def _calculate_overall_response_rate(self, communications: List[Dict]) -> float:
        """Calculate overall response rate for communications."""
        try:
            outbound_comms = [c for c in communications if c.get("direction") == "outbound"]
            if not outbound_comms:
                return 0.0

            responded_comms = [c for c in outbound_comms if c.get("response_received", False)]
            return (len(responded_comms) / len(outbound_comms)) * 100
        except Exception as e:
            logger.error(f"Error calculating overall response rate: {e}")
            return 0.0


# Global service instance
_firebase_crm_service = None


def get_firebase_crm_service():
    """Get or create Firebase CRM service instance.

    Returns:
        FirebaseCRMService: Firebase CRM service instance
    """
    global _firebase_crm_service

    if _firebase_crm_service is None:
        from langflow.services.database.providers.firebase import get_firebase_provider
        db_provider = get_firebase_provider()
        _firebase_crm_service = FirebaseCRMService(db_provider)

    return _firebase_crm_service
