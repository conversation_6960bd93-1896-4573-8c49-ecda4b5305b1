"""
Celery Background Tasks for Video Export
Handles asynchronous video export processing with progress tracking.
"""

import asyncio
import json
import os
from datetime import datetime, timezone
from typing import Any, Dict, Optional

from celery import Celery
from celery.exceptions import WorkerLostError

from langflow.services.export.export_service import professional_export_service, ExportError
from langflow.logging.logger import logger

# Initialize Celery app
celery_app = Celery("video_export")
celery_app.config_from_object("langflow.core.celeryconfig")


class ExportProgress:
    """Helper class to track and report export progress."""
    
    def __init__(self, task, export_id: str):
        self.task = task
        self.export_id = export_id
        self.last_progress = 0.0
    
    async def __call__(self, progress_data: Dict[str, Any]) -> None:
        """Progress callback for export operations."""
        try:
            progress = progress_data.get("progress", 0.0)
            status = progress_data.get("status", "Processing...")
            
            # Only update if progress has changed significantly
            if abs(progress - self.last_progress) >= 1.0 or "completed" in status.lower():
                self.task.update_state(
                    state="PROGRESS",
                    meta={
                        "export_id": self.export_id,
                        "current": progress,
                        "total": 100,
                        "status": status,
                        "current_time": progress_data.get("current_time"),
                        "total_duration": progress_data.get("total_duration"),
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
                self.last_progress = progress
                
                logger.info(f"Export {self.export_id} progress: {progress:.1f}% - {status}")
                
        except Exception as e:
            logger.error(f"Failed to update export progress: {e}")


@celery_app.task(bind=True, name="export_video")
def export_video_task(
    self,
    export_id: str,
    timeline_data: Dict[str, Any],
    export_settings: Dict[str, Any],
    output_path: str,
    user_id: str,
    project_id: str
) -> Dict[str, Any]:
    """
    Background task to process video export.
    
    Args:
        export_id: Unique identifier for the export job
        timeline_data: Timeline data containing clips and effects
        export_settings: Export configuration settings
        output_path: Path where the exported video will be saved
        user_id: ID of the user who initiated the export
        project_id: ID of the project being exported
    
    Returns:
        Dict containing export result information
    """
    try:
        logger.info(f"Starting export task {export_id} for user {user_id}")
        
        # Update initial state
        self.update_state(
            state="PROGRESS",
            meta={
                "export_id": export_id,
                "current": 0,
                "total": 100,
                "status": "Initializing export...",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Create progress callback
        progress_callback = ExportProgress(self, export_id)
        
        # Run the export in async context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                professional_export_service.export_video(
                    timeline_data=timeline_data,
                    export_settings=export_settings,
                    output_path=output_path,
                    progress_callback=progress_callback
                )
            )
        finally:
            loop.close()
        
        # Mark as completed
        completion_data = {
            "export_id": export_id,
            "status": "completed",
            "output_path": output_path,
            "file_size": result.get("file_size"),
            "encoder": result.get("encoder"),
            "gpu_type": result.get("gpu_type"),
            "completed_at": result.get("completed_at"),
            "upload_url": result.get("upload", {}).get("url"),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"Export task {export_id} completed successfully")
        return completion_data
        
    except ExportError as e:
        error_msg = f"Export failed: {str(e)}"
        logger.error(f"Export task {export_id} failed: {error_msg}")
        
        self.update_state(
            state="FAILURE",
            meta={
                "export_id": export_id,
                "error": error_msg,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        raise
        
    except WorkerLostError:
        error_msg = "Export worker was lost (system resources may be insufficient)"
        logger.error(f"Export task {export_id} worker lost")
        raise
        
    except Exception as e:
        error_msg = f"Unexpected error during export: {str(e)}"
        logger.error(f"Export task {export_id} unexpected error: {error_msg}")
        
        self.update_state(
            state="FAILURE",
            meta={
                "export_id": export_id,
                "error": error_msg,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        raise


@celery_app.task(bind=True, name="upload_to_platform")
def upload_to_platform_task(
    self,
    export_id: str,
    video_path: str,
    platform: str,
    metadata: Dict[str, Any],
    credentials: Dict[str, str],
    user_id: str
) -> Dict[str, Any]:
    """
    Background task to upload video to social media platform.
    
    Args:
        export_id: Export ID for tracking
        video_path: Path to the exported video file
        platform: Target platform (youtube, vimeo, etc.)
        metadata: Upload metadata (title, description, etc.)
        credentials: Platform API credentials
        user_id: ID of the user who initiated the upload
    
    Returns:
        Dict containing upload result information
    """
    try:
        logger.info(f"Starting upload task for export {export_id} to {platform}")
        
        # Update initial state
        self.update_state(
            state="PROGRESS",
            meta={
                "export_id": export_id,
                "current": 0,
                "total": 100,
                "status": f"Preparing upload to {platform}...",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
        # Verify video file exists
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        # Update progress
        self.update_state(
            state="PROGRESS",
            meta={
                "export_id": export_id,
                "current": 10,
                "total": 100,
                "status": f"Uploading to {platform}...",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
        # Run the upload in async context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            upload_result = loop.run_until_complete(
                professional_export_service.platform_uploader.upload_to_platform(
                    video_path=video_path,
                    platform=platform,
                    metadata=metadata,
                    credentials=credentials
                )
            )
        finally:
            loop.close()
        
        # Mark as completed
        completion_data = {
            "export_id": export_id,
            "status": "uploaded",
            "platform": platform,
            "video_id": upload_result.get("video_id"),
            "url": upload_result.get("url"),
            "uploaded_at": upload_result.get("uploaded_at"),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"Upload task for export {export_id} completed successfully")
        return completion_data
        
    except Exception as e:
        error_msg = f"Upload failed: {str(e)}"
        logger.error(f"Upload task for export {export_id} failed: {error_msg}")
        
        self.update_state(
            state="FAILURE",
            meta={
                "export_id": export_id,
                "error": error_msg,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        raise


@celery_app.task(name="cleanup_expired_exports")
def cleanup_expired_exports() -> Dict[str, Any]:
    """
    Periodic task to clean up expired export files and jobs.
    """
    try:
        logger.info("Starting cleanup of expired exports")
        
        cleaned_files = 0
        cleaned_jobs = 0
        
        # Clean up old export files (older than 7 days)
        export_dir = "/tmp/exports"
        if os.path.exists(export_dir):
            current_time = datetime.now(timezone.utc)
            
            for filename in os.listdir(export_dir):
                file_path = os.path.join(export_dir, filename)
                
                try:
                    file_mtime = datetime.fromtimestamp(
                        os.path.getmtime(file_path), 
                        tz=timezone.utc
                    )
                    
                    # Remove files older than 7 days
                    age_days = (current_time - file_mtime).days
                    if age_days > 7:
                        os.remove(file_path)
                        cleaned_files += 1
                        logger.info(f"Cleaned up expired export file: {filename}")
                        
                except Exception as e:
                    logger.warning(f"Failed to clean up file {filename}: {e}")
        
        # In a real implementation, you'd also clean up database records
        # for completed/failed exports older than a certain threshold
        
        result = {
            "cleaned_files": cleaned_files,
            "cleaned_jobs": cleaned_jobs,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"Export cleanup completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Export cleanup failed: {e}")
        raise


@celery_app.task(name="generate_export_analytics")
def generate_export_analytics(period_days: int = 30) -> Dict[str, Any]:
    """
    Generate analytics for export usage over a specified period.
    
    Args:
        period_days: Number of days to include in analytics
    
    Returns:
        Dict containing export analytics data
    """
    try:
        logger.info(f"Generating export analytics for {period_days} days")
        
        # In a real implementation, you'd query the database for export statistics
        analytics = {
            "period_days": period_days,
            "total_exports": 145,
            "successful_exports": 138,
            "failed_exports": 7,
            "success_rate": 0.952,
            "popular_presets": {
                "youtube_1080p": 45,
                "instagram_story": 32,
                "web_hd": 28,
                "youtube_4k": 22,
                "tiktok": 18
            },
            "hardware_usage": {
                "nvidia_gpu": 0.65,
                "intel_qsv": 0.23,
                "cpu_only": 0.12
            },
            "average_export_time": 127,  # seconds
            "total_output_size_gb": 45.7,
            "platform_uploads": {
                "youtube": 34,
                "vimeo": 12,
                "instagram": 8,
                "tiktok": 6
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        logger.info("Export analytics generated successfully")
        return analytics
        
    except Exception as e:
        logger.error(f"Failed to generate export analytics: {e}")
        raise


# Task routing configuration
celery_app.conf.task_routes = {
    "export_video": {"queue": "exports"},
    "upload_to_platform": {"queue": "uploads"},
    "cleanup_expired_exports": {"queue": "maintenance"},
    "generate_export_analytics": {"queue": "analytics"}
}

# Periodic task schedule
celery_app.conf.beat_schedule = {
    "cleanup-exports": {
        "task": "cleanup_expired_exports",
        "schedule": 86400.0,  # Run daily
    },
    "export-analytics": {
        "task": "generate_export_analytics",
        "schedule": 3600.0,  # Run hourly
        "kwargs": {"period_days": 7}
    }
}

celery_app.conf.timezone = "UTC"