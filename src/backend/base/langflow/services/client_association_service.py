"""
Client Association Service
Handles automatic client association for new content across all modules
"""

import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from langflow.services.client_context_service import ClientContextService
from langflow.services.synchronization_service import sync_service, SyncEvent

logger = logging.getLogger(__name__)


class AssociationType(Enum):
    """Types of client associations"""
    AUTOMATIC = "automatic"
    MANUAL = "manual"
    INHERITED = "inherited"
    BULK = "bulk"
    OVERRIDE = "override"


@dataclass
class AssociationRule:
    """Defines rules for automatic client association"""
    entity_type: str
    module: str
    conditions: Dict[str, Any]
    priority: int = 1
    association_type: AssociationType = AssociationType.AUTOMATIC
    inherit_from_parent: bool = True
    require_confirmation: bool = False


@dataclass
class AssociationResult:
    """Result of an association attempt"""
    success: bool
    client_id: Optional[str]
    association_type: AssociationType
    confidence: float
    reason: str
    suggestions: List[str] = None
    requires_confirmation: bool = False


class ClientAssociationService:
    """Service for automatic client association across modules"""
    
    def __init__(self):
        from langflow.services.database.providers.firebase_crm_service import get_firebase_crm_service
        crm_service = get_firebase_crm_service()
        self.context_service = ClientContextService(crm_service)
        self.association_rules: List[AssociationRule] = []
        self.entity_inheritance_map: Dict[str, str] = {}
        self.active_associations: Dict[str, str] = {}  # session_id -> client_id
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default association rules"""
        self.association_rules = [
            # CRM Module Rules
            AssociationRule(
                entity_type="communication",
                module="crm",
                conditions={"has_client_context": True},
                priority=1,
                inherit_from_parent=False
            ),
            AssociationRule(
                entity_type="contact_relationship",
                module="crm",
                conditions={"client_id_in_data": True},
                priority=1,
                inherit_from_parent=False
            ),
            
            # Project Management Rules
            AssociationRule(
                entity_type="project",
                module="project_management",
                conditions={"has_active_client": True},
                priority=1,
                inherit_from_parent=False
            ),
            AssociationRule(
                entity_type="task",
                module="project_management",
                conditions={"parent_project_exists": True},
                priority=2,
                inherit_from_parent=True
            ),
            
            # Video Editor Rules
            AssociationRule(
                entity_type="video_project",
                module="video_editor",
                conditions={"has_active_client": True},
                priority=1,
                inherit_from_parent=False
            ),
            AssociationRule(
                entity_type="video_asset",
                module="video_editor",
                conditions={"parent_project_exists": True},
                priority=2,
                inherit_from_parent=True
            ),
            
            # Book Editor Rules
            AssociationRule(
                entity_type="book_project",
                module="book_editor",
                conditions={"has_active_client": True},
                priority=1,
                inherit_from_parent=False
            ),
            AssociationRule(
                entity_type="chapter",
                module="book_editor",
                conditions={"parent_book_exists": True},
                priority=2,
                inherit_from_parent=True
            ),
            
            # Design Editor Rules
            AssociationRule(
                entity_type="design_project",
                module="design_editor",
                conditions={"has_active_client": True},
                priority=1,
                inherit_from_parent=False
            ),
            AssociationRule(
                entity_type="design_asset",
                module="design_editor",
                conditions={"parent_project_exists": True},
                priority=2,
                inherit_from_parent=True
            )
        ]
        
        # Define inheritance relationships
        self.entity_inheritance_map = {
            "task": "project",
            "video_asset": "video_project",
            "chapter": "book_project",
            "design_asset": "design_project",
            "project_milestone": "project",
            "communication_attachment": "communication"
        }
    
    async def associate_with_client(
        self,
        entity_data: Dict,
        entity_type: str,
        module: str,
        workspace_id: str,
        user_id: str,
        session_id: Optional[str] = None
    ) -> AssociationResult:
        """
        Associate entity with appropriate client based on rules and context
        """
        try:
            # Check if client is already specified
            if entity_data.get('client_id'):
                return AssociationResult(
                    success=True,
                    client_id=entity_data['client_id'],
                    association_type=AssociationType.MANUAL,
                    confidence=1.0,
                    reason="Client explicitly specified"
                )
            
            # Find applicable association rules
            applicable_rules = [
                rule for rule in self.association_rules
                if rule.entity_type == entity_type and rule.module == module
            ]
            
            # Sort by priority
            applicable_rules.sort(key=lambda x: x.priority)
            
            # Try each rule
            for rule in applicable_rules:
                result = await self._apply_association_rule(
                    rule, entity_data, workspace_id, user_id, session_id
                )
                
                if result.success:
                    await self._log_association(
                        entity_type=entity_type,
                        entity_id=entity_data.get('id', 'unknown'),
                        client_id=result.client_id,
                        association_type=result.association_type,
                        workspace_id=workspace_id,
                        user_id=user_id,
                        confidence=result.confidence,
                        reason=result.reason
                    )
                    
                    return result
            
            # No rules matched, try suggestions
            suggestions = await self._get_association_suggestions(
                entity_data, entity_type, module, workspace_id, user_id
            )
            
            return AssociationResult(
                success=False,
                client_id=None,
                association_type=AssociationType.MANUAL,
                confidence=0.0,
                reason="No automatic association rules matched",
                suggestions=suggestions,
                requires_confirmation=True
            )
        
        except Exception as e:
            logger.error(f"Error in client association: {e}")
            return AssociationResult(
                success=False,
                client_id=None,
                association_type=AssociationType.AUTOMATIC,
                confidence=0.0,
                reason=f"Association error: {str(e)}"
            )
    
    async def _apply_association_rule(
        self,
        rule: AssociationRule,
        entity_data: Dict,
        workspace_id: str,
        user_id: str,
        session_id: Optional[str]
    ) -> AssociationResult:
        """Apply specific association rule"""
        
        # Check if conditions are met
        conditions_met = await self._check_rule_conditions(
            rule.conditions, entity_data, workspace_id, user_id, session_id
        )
        
        if not conditions_met:
            return AssociationResult(
                success=False,
                client_id=None,
                association_type=rule.association_type,
                confidence=0.0,
                reason=f"Rule conditions not met for {rule.entity_type}"
            )
        
        # Get client ID based on rule type
        if rule.inherit_from_parent and rule.entity_type in self.entity_inheritance_map:
            client_id = await self._get_inherited_client_id(
                entity_data, rule.entity_type, workspace_id
            )
            association_type = AssociationType.INHERITED
        else:
            client_id = await self._get_active_client_id(workspace_id, user_id, session_id)
            association_type = rule.association_type
        
        if client_id:
            confidence = await self._calculate_association_confidence(
                entity_data, client_id, rule, workspace_id
            )
            
            return AssociationResult(
                success=True,
                client_id=client_id,
                association_type=association_type,
                confidence=confidence,
                reason=f"Associated via {rule.entity_type} rule",
                requires_confirmation=rule.require_confirmation
            )
        
        return AssociationResult(
            success=False,
            client_id=None,
            association_type=rule.association_type,
            confidence=0.0,
            reason="No active client context found"
        )
    
    async def _check_rule_conditions(
        self,
        conditions: Dict[str, Any],
        entity_data: Dict,
        workspace_id: str,
        user_id: str,
        session_id: Optional[str]
    ) -> bool:
        """Check if rule conditions are satisfied"""
        
        for condition, expected_value in conditions.items():
            if condition == "has_client_context":
                active_client = await self._get_active_client_id(workspace_id, user_id, session_id)
                if (active_client is not None) != expected_value:
                    return False
            
            elif condition == "client_id_in_data":
                has_client_id = 'client_id' in entity_data and entity_data['client_id']
                if has_client_id != expected_value:
                    return False
            
            elif condition == "has_active_client":
                active_client = await self._get_active_client_id(workspace_id, user_id, session_id)
                if (active_client is not None) != expected_value:
                    return False
            
            elif condition == "parent_project_exists":
                parent_exists = await self._check_parent_exists(entity_data, workspace_id)
                if parent_exists != expected_value:
                    return False
            
            elif condition == "parent_book_exists":
                parent_exists = await self._check_parent_book_exists(entity_data, workspace_id)
                if parent_exists != expected_value:
                    return False
        
        return True
    
    async def _get_active_client_id(
        self,
        workspace_id: str,
        user_id: str,
        session_id: Optional[str]
    ) -> Optional[str]:
        """Get currently active client ID from session or context"""
        
        # Check session-specific associations first
        if session_id and session_id in self.active_associations:
            return self.active_associations[session_id]
        
        # Check user's last active client in workspace
        # This would integrate with actual user session service
        # For now, return None to indicate no active client
        return None
    
    async def _get_inherited_client_id(
        self,
        entity_data: Dict,
        entity_type: str,
        workspace_id: str
    ) -> Optional[str]:
        """Get client ID from parent entity"""
        
        parent_type = self.entity_inheritance_map.get(entity_type)
        if not parent_type:
            return None
        
        # Look for parent ID in entity data
        parent_id_fields = [
            f"{parent_type}_id",
            "parent_id",
            "project_id",
            "book_id",
            "communication_id"
        ]
        
        parent_id = None
        for field in parent_id_fields:
            if field in entity_data:
                parent_id = entity_data[field]
                break
        
        if not parent_id:
            return None
        
        # Query parent entity for client_id
        # This would integrate with actual database services
        # For now, return None
        return None
    
    async def _check_parent_exists(self, entity_data: Dict, workspace_id: str) -> bool:
        """Check if parent project exists"""
        project_id = entity_data.get('project_id')
        if not project_id:
            return False
        
        # This would check actual project existence
        return True
    
    async def _check_parent_book_exists(self, entity_data: Dict, workspace_id: str) -> bool:
        """Check if parent book exists"""
        book_id = entity_data.get('book_id')
        if not book_id:
            return False
        
        # This would check actual book existence
        return True
    
    async def _calculate_association_confidence(
        self,
        entity_data: Dict,
        client_id: str,
        rule: AssociationRule,
        workspace_id: str
    ) -> float:
        """Calculate confidence score for association"""
        
        base_confidence = 0.8  # Base confidence for rule-based associations
        
        # Adjust based on rule priority
        priority_adjustment = 1.0 - (rule.priority - 1) * 0.1
        
        # Adjust based on data completeness
        data_fields = ['name', 'description', 'client_id', 'project_id']
        filled_fields = sum(1 for field in data_fields if entity_data.get(field))
        data_completeness = filled_fields / len(data_fields)
        
        confidence = base_confidence * priority_adjustment * (0.5 + 0.5 * data_completeness)
        
        return min(1.0, max(0.0, confidence))
    
    async def _get_association_suggestions(
        self,
        entity_data: Dict,
        entity_type: str,
        module: str,
        workspace_id: str,
        user_id: str
    ) -> List[str]:
        """Get client association suggestions when automatic association fails"""
        
        suggestions = []
        
        # Suggest recent clients for this user
        # This would integrate with actual user activity tracking
        suggestions.extend([
            "Consider recent clients you've worked with",
            "Check if this relates to an existing project",
            "Verify client context is set in your session"
        ])
        
        # Entity-specific suggestions
        if entity_type in ["project", "video_project", "book_project", "design_project"]:
            suggestions.append("Major projects usually require explicit client assignment")
        
        elif entity_type in ["task", "chapter", "video_asset", "design_asset"]:
            suggestions.append("Check if parent project has client association")
        
        return suggestions
    
    async def _log_association(
        self,
        entity_type: str,
        entity_id: str,
        client_id: str,
        association_type: AssociationType,
        workspace_id: str,
        user_id: str,
        confidence: float,
        reason: str
    ):
        """Log association event"""
        
        # Create sync event for association
        event = SyncEvent(
            event_type="client_associated",
            client_id=client_id,
            workspace_id=workspace_id,
            module="association_service",
            entity_type=entity_type,
            entity_id=entity_id,
            data={
                "association_type": association_type.value,
                "confidence": confidence,
                "reason": reason,
                "user_id": user_id
            },
            timestamp=datetime.utcnow(),
            priority=2
        )
        
        await sync_service.emit_sync_event(event)
        
        logger.info(
            f"Associated {entity_type} {entity_id} with client {client_id} "
            f"({association_type.value}, confidence: {confidence:.2f})"
        )
    
    async def set_active_client(self, session_id: str, client_id: str):
        """Set active client for session"""
        self.active_associations[session_id] = client_id
        logger.info(f"Set active client {client_id} for session {session_id}")
    
    async def clear_active_client(self, session_id: str):
        """Clear active client for session"""
        if session_id in self.active_associations:
            del self.active_associations[session_id]
            logger.info(f"Cleared active client for session {session_id}")
    
    async def get_active_client(self, session_id: str) -> Optional[str]:
        """Get active client for session"""
        return self.active_associations.get(session_id)
    
    async def bulk_associate_entities(
        self,
        entity_ids: List[str],
        entity_type: str,
        module: str,
        client_id: str,
        workspace_id: str,
        user_id: str
    ) -> Dict[str, AssociationResult]:
        """Bulk associate multiple entities with a client"""
        
        results = {}
        
        for entity_id in entity_ids:
            try:
                # This would update the actual entity in the database
                # For now, just create the result
                results[entity_id] = AssociationResult(
                    success=True,
                    client_id=client_id,
                    association_type=AssociationType.BULK,
                    confidence=1.0,
                    reason="Bulk association operation"
                )
                
                # Log the association
                await self._log_association(
                    entity_type=entity_type,
                    entity_id=entity_id,
                    client_id=client_id,
                    association_type=AssociationType.BULK,
                    workspace_id=workspace_id,
                    user_id=user_id,
                    confidence=1.0,
                    reason="Bulk association operation"
                )
                
            except Exception as e:
                results[entity_id] = AssociationResult(
                    success=False,
                    client_id=None,
                    association_type=AssociationType.BULK,
                    confidence=0.0,
                    reason=f"Bulk association failed: {str(e)}"
                )
        
        return results
    
    async def override_association(
        self,
        entity_id: str,
        entity_type: str,
        module: str,
        new_client_id: str,
        workspace_id: str,
        user_id: str,
        reason: str = "Manual override"
    ) -> AssociationResult:
        """Override existing client association"""
        
        try:
            # This would update the actual entity in the database
            # For now, just create the result
            result = AssociationResult(
                success=True,
                client_id=new_client_id,
                association_type=AssociationType.OVERRIDE,
                confidence=1.0,
                reason=reason
            )
            
            # Log the override
            await self._log_association(
                entity_type=entity_type,
                entity_id=entity_id,
                client_id=new_client_id,
                association_type=AssociationType.OVERRIDE,
                workspace_id=workspace_id,
                user_id=user_id,
                confidence=1.0,
                reason=reason
            )
            
            return result
            
        except Exception as e:
            return AssociationResult(
                success=False,
                client_id=None,
                association_type=AssociationType.OVERRIDE,
                confidence=0.0,
                reason=f"Override failed: {str(e)}"
            )
    
    async def validate_associations(self, workspace_id: str) -> Dict[str, Any]:
        """Validate all client associations in workspace"""
        
        validation_results = {
            "workspace_id": workspace_id,
            "total_entities": 0,
            "valid_associations": 0,
            "invalid_associations": 0,
            "orphaned_entities": 0,
            "issues": [],
            "recommendations": []
        }
        
        # This would perform actual validation against database
        # For now, return mock results
        validation_results.update({
            "total_entities": 150,
            "valid_associations": 142,
            "invalid_associations": 3,
            "orphaned_entities": 5,
            "recommendations": [
                "Review orphaned entities and assign appropriate clients",
                "Validate invalid associations manually",
                "Consider updating association rules for better coverage"
            ]
        })
        
        return validation_results


# Global instance
association_service = ClientAssociationService()