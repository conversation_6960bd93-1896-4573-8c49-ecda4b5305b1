"""
Video Processing Service with FFmpeg Integration
Provides professional-grade video processing capabilities for the video editor.
"""

import asyncio
import json
import logging
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from uuid import uuid4
import os

from langflow.logging.logger import logger


class VideoProcessingError(Exception):
    """Base exception for video processing errors."""
    pass


class FFmpegNotFoundError(VideoProcessingError):
    """Raised when FFmpeg is not found."""
    pass


class VideoProcessingService:
    """Service for handling video processing operations with FFmpeg."""
    
    def __init__(self):
        self.ffmpeg_path = None
        self._ffmpeg_checked = False
        self.supported_formats = [
            "mp4", "mov", "avi", "webm", "mkv", "flv", "wmv", "3gp", "m4v"
        ]
        self.supported_codecs = {
            "video": ["h264", "h265", "vp8", "vp9", "av1"],
            "audio": ["aac", "mp3", "opus", "vorbis", "flac"]
        }
        
    def _find_ffmpeg(self) -> str:
        """Find FFmpeg executable on the system."""
        ffmpeg_candidates = ["ffmpeg", "/usr/bin/ffmpeg", "/usr/local/bin/ffmpeg"]
        
        for candidate in ffmpeg_candidates:
            if shutil.which(candidate):
                return candidate
                
        raise FFmpegNotFoundError(
            "FFmpeg not found. Please install FFmpeg and ensure it's in your PATH."
        )
    
    def _ensure_ffmpeg(self) -> str:
        """Ensure FFmpeg is available and return its path."""
        if not self._ffmpeg_checked:
            try:
                self.ffmpeg_path = self._find_ffmpeg()
                self._ffmpeg_checked = True
                logger.info(f"FFmpeg found at: {self.ffmpeg_path}")
            except FFmpegNotFoundError as e:
                logger.warning(f"FFmpeg not available: {e}")
                self._ffmpeg_checked = True
                raise
        
        if not self.ffmpeg_path:
            raise FFmpegNotFoundError(
                "FFmpeg not found. Please install FFmpeg and ensure it's in your PATH."
            )
        
        return self.ffmpeg_path
    
    async def check_ffmpeg_version(self) -> Dict[str, str]:
        """Check FFmpeg version and capabilities."""
        try:
            ffmpeg_path = self._ensure_ffmpeg()
            process = await asyncio.create_subprocess_exec(
                ffmpeg_path, "-version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise VideoProcessingError(f"FFmpeg version check failed: {stderr.decode()}")
                
            version_info = stdout.decode()
            lines = version_info.split('\n')
            
            return {
                "version": lines[0] if lines else "Unknown",
                "configuration": next((line for line in lines if "configuration:" in line), ""),
                "status": "available"
            }
            
        except Exception as e:
            logger.error(f"Failed to check FFmpeg version: {e}")
            raise VideoProcessingError(f"FFmpeg version check failed: {e}")
    
    async def validate_video_file(self, file_path: str) -> Dict:
        """
        Validate video file and extract metadata using FFprobe.
        
        Args:
            file_path: Path to the video file
            
        Returns:
            Dictionary containing video metadata
        """
        if not Path(file_path).exists():
            raise VideoProcessingError(f"Video file not found: {file_path}")
            
        try:
            # Use FFprobe to get detailed information about the video
            cmd = [
                "ffprobe", "-v", "quiet", "-print_format", "json",
                "-show_format", "-show_streams", file_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise VideoProcessingError(f"Video validation failed: {stderr.decode()}")
                
            probe_data = json.loads(stdout.decode())
            return self._extract_video_metadata(probe_data)
            
        except json.JSONDecodeError as e:
            raise VideoProcessingError(f"Failed to parse video metadata: {e}")
        except Exception as e:
            logger.error(f"Video validation failed for {file_path}: {e}")
            raise VideoProcessingError(f"Video validation failed: {e}")
    
    def _extract_video_metadata(self, probe_data: Dict) -> Dict:
        """Extract useful metadata from FFprobe output."""
        format_info = probe_data.get("format", {})
        streams = probe_data.get("streams", [])
        
        video_stream = next((s for s in streams if s.get("codec_type") == "video"), None)
        audio_streams = [s for s in streams if s.get("codec_type") == "audio"]
        
        metadata = {
            "format": {
                "filename": format_info.get("filename", ""),
                "format_name": format_info.get("format_name", ""),
                "duration": float(format_info.get("duration", 0)),
                "size": int(format_info.get("size", 0)),
                "bit_rate": int(format_info.get("bit_rate", 0))
            },
            "video": None,
            "audio": []
        }
        
        if video_stream:
            metadata["video"] = {
                "codec": video_stream.get("codec_name", ""),
                "width": int(video_stream.get("width", 0)),
                "height": int(video_stream.get("height", 0)),
                "fps": self._parse_frame_rate(video_stream.get("r_frame_rate", "0/1")),
                "bit_rate": int(video_stream.get("bit_rate", 0)),
                "pixel_format": video_stream.get("pix_fmt", ""),
                "color_space": video_stream.get("color_space", ""),
                "duration": float(video_stream.get("duration", 0))
            }
            
        for audio_stream in audio_streams:
            metadata["audio"].append({
                "codec": audio_stream.get("codec_name", ""),
                "sample_rate": int(audio_stream.get("sample_rate", 0)),
                "channels": int(audio_stream.get("channels", 0)),
                "channel_layout": audio_stream.get("channel_layout", ""),
                "bit_rate": int(audio_stream.get("bit_rate", 0)),
                "duration": float(audio_stream.get("duration", 0))
            })
            
        return metadata
    
    def _parse_frame_rate(self, frame_rate_str: str) -> float:
        """Parse frame rate from string like '30/1' to float."""
        try:
            if "/" in frame_rate_str:
                num, den = frame_rate_str.split("/")
                return float(num) / float(den) if float(den) != 0 else 0.0
            return float(frame_rate_str)
        except (ValueError, ZeroDivisionError):
            return 0.0
    
    async def transcode_video(
        self,
        input_path: str,
        output_path: str,
        preset: str = "medium",
        quality: str = "high",
        progress_callback: Optional[callable] = None
    ) -> str:
        """
        Transcode video with specified settings.
        
        Args:
            input_path: Source video file path
            output_path: Destination video file path
            preset: Encoding preset (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
            quality: Quality level (proxy, web, high, broadcast)
            progress_callback: Optional callback for progress updates
            
        Returns:
            Path to the transcoded video file
        """
        quality_settings = self._get_quality_settings(quality)
        
        cmd = [
            self._ensure_ffmpeg(),
            "-i", input_path,
            "-c:v", quality_settings["video_codec"],
            "-preset", preset,
            "-crf", str(quality_settings["crf"]),
            "-c:a", quality_settings["audio_codec"],
            "-b:a", quality_settings["audio_bitrate"],
            "-movflags", "+faststart",  # Enable progressive download
            "-y",  # Overwrite output file
            output_path
        ]
        
        # Add resolution scaling if specified
        if quality_settings.get("scale"):
            cmd.extend(["-vf", f"scale={quality_settings['scale']}"])
            
        logger.info(f"Starting video transcoding: {input_path} -> {output_path}")
        logger.debug(f"FFmpeg command: {' '.join(cmd)}")
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Monitor progress if callback provided
            if progress_callback:
                await self._monitor_progress(process, progress_callback, input_path)
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown error"
                raise VideoProcessingError(f"Video transcoding failed: {error_msg}")
                
            logger.info(f"Video transcoding completed: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Video transcoding failed: {e}")
            # Clean up failed output file
            if Path(output_path).exists():
                Path(output_path).unlink()
            raise VideoProcessingError(f"Video transcoding failed: {e}")
    
    def _get_quality_settings(self, quality: str) -> Dict:
        """Get encoding settings for specified quality level."""
        settings = {
            "proxy": {
                "video_codec": "libx264",
                "audio_codec": "aac",
                "crf": 28,
                "audio_bitrate": "96k",
                "scale": "854:480"
            },
            "web": {
                "video_codec": "libx264",
                "audio_codec": "aac",
                "crf": 23,
                "audio_bitrate": "128k",
                "scale": "1280:720"
            },
            "high": {
                "video_codec": "libx264",
                "audio_codec": "aac",
                "crf": 18,
                "audio_bitrate": "192k"
            },
            "broadcast": {
                "video_codec": "libx264",
                "audio_codec": "pcm_s16le",
                "crf": 15,
                "audio_bitrate": "1411k"
            }
        }
        
        return settings.get(quality, settings["high"])
    
    async def _monitor_progress(
        self,
        process: asyncio.subprocess.Process,
        progress_callback: callable,
        input_path: str
    ):
        """Monitor FFmpeg progress and call progress callback."""
        # Get total duration for progress calculation
        try:
            metadata = await self.validate_video_file(input_path)
            total_duration = metadata["format"]["duration"]
        except Exception:
            total_duration = 0
            
        while True:
            try:
                line = await asyncio.wait_for(
                    process.stderr.readline(), timeout=1.0
                )
                if not line:
                    break
                    
                line_str = line.decode().strip()
                
                # Parse progress from FFmpeg output
                if "time=" in line_str and total_duration > 0:
                    try:
                        time_part = line_str.split("time=")[1].split()[0]
                        current_time = self._parse_time_to_seconds(time_part)
                        progress = min(100, (current_time / total_duration) * 100)
                        
                        await progress_callback({
                            "progress": progress,
                            "current_time": current_time,
                            "total_duration": total_duration,
                            "status": "processing"
                        })
                    except (IndexError, ValueError):
                        continue
                        
            except asyncio.TimeoutError:
                continue
            except Exception:
                break
    
    def _parse_time_to_seconds(self, time_str: str) -> float:
        """Parse time string (HH:MM:SS.mmm) to seconds."""
        try:
            parts = time_str.split(":")
            if len(parts) == 3:
                hours, minutes, seconds = parts
                return float(hours) * 3600 + float(minutes) * 60 + float(seconds)
        except (ValueError, IndexError):
            pass
        return 0.0
    
    async def generate_thumbnail(
        self,
        input_path: str,
        output_path: str,
        timestamp: float = 0.0,
        width: int = 320,
        height: int = 240
    ) -> str:
        """
        Generate a thumbnail from a video at specified timestamp.
        
        Args:
            input_path: Source video file path
            output_path: Output thumbnail image path
            timestamp: Time in seconds to capture thumbnail
            width: Thumbnail width
            height: Thumbnail height
            
        Returns:
            Path to the generated thumbnail
        """
        cmd = [
            self._ensure_ffmpeg(),
            "-ss", str(timestamp),
            "-i", input_path,
            "-vframes", "1",
            "-vf", f"scale={width}:{height}",
            "-y",
            output_path
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown error"
                raise VideoProcessingError(f"Thumbnail generation failed: {error_msg}")
                
            logger.info(f"Thumbnail generated: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Thumbnail generation failed: {e}")
            if Path(output_path).exists():
                Path(output_path).unlink()
            raise VideoProcessingError(f"Thumbnail generation failed: {e}")
    
    async def generate_waveform(
        self,
        input_path: str,
        output_path: str,
        width: int = 1920,
        height: int = 200
    ) -> str:
        """
        Generate audio waveform visualization from video/audio file.
        
        Args:
            input_path: Source file path
            output_path: Output waveform image path
            width: Waveform width
            height: Waveform height
            
        Returns:
            Path to the generated waveform image
        """
        cmd = [
            self._ensure_ffmpeg(),
            "-i", input_path,
            "-filter_complex", 
            f"[0:a]showwavespic=s={width}x{height}:colors=0x4CAF50[v]",
            "-map", "[v]",
            "-frames:v", "1",
            "-y",
            output_path
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown error"
                raise VideoProcessingError(f"Waveform generation failed: {error_msg}")
                
            logger.info(f"Waveform generated: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Waveform generation failed: {e}")
            if Path(output_path).exists():
                Path(output_path).unlink()
            raise VideoProcessingError(f"Waveform generation failed: {e}")
    
    async def extract_audio(
        self,
        input_path: str,
        output_path: str,
        codec: str = "aac",
        bitrate: str = "192k"
    ) -> str:
        """
        Extract audio track from video file.
        
        Args:
            input_path: Source video file path
            output_path: Output audio file path
            codec: Audio codec (aac, mp3, wav)
            bitrate: Audio bitrate
            
        Returns:
            Path to the extracted audio file
        """
        cmd = [
            self._ensure_ffmpeg(),
            "-i", input_path,
            "-vn",  # No video
            "-c:a", codec,
            "-b:a", bitrate,
            "-y",
            output_path
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown error"
                raise VideoProcessingError(f"Audio extraction failed: {error_msg}")
                
            logger.info(f"Audio extracted: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Audio extraction failed: {e}")
            if Path(output_path).exists():
                Path(output_path).unlink()
            raise VideoProcessingError(f"Audio extraction failed: {e}")
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported video formats."""
        return self.supported_formats.copy()
    
    def is_format_supported(self, file_path: str) -> bool:
        """Check if file format is supported."""
        extension = Path(file_path).suffix.lower().lstrip(".")
        return extension in self.supported_formats
    
    def is_available(self) -> bool:
        """Check if video processing is available (FFmpeg is installed)."""
        try:
            self._ensure_ffmpeg()
            return True
        except FFmpegNotFoundError:
            return False


# Global instance
video_processing_service = VideoProcessingService()