"""
Celery tasks for background video processing.
"""

import asyncio
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Optional
from uuid import UUID

from langflow.core.celery_app import celery_app as celery
from langflow.services.video_processing.video_processing_service import video_processing_service
from langflow.logging.logger import logger


@celery.task(bind=True, name="process_video_upload")
def process_video_upload(
    self,
    video_asset_id: str,
    input_path: str,
    user_id: str,
    workspace_id: Optional[str] = None
) -> Dict:
    """
    Background task to process uploaded video file.
    
    Args:
        video_asset_id: UUID of the video asset
        input_path: Path to the uploaded video file
        user_id: ID of the user who uploaded the video
        workspace_id: Optional workspace ID
        
    Returns:
        Dictionary with processing results
    """
    try:
        # Update task status
        self.update_state(
            state="PROGRESS",
            meta={
                "current": 5,
                "total": 100,
                "status": "Validating video file...",
                "video_asset_id": video_asset_id
            }
        )
        
        # Run async validation in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Validate and extract metadata
            metadata = loop.run_until_complete(
                video_processing_service.validate_video_file(input_path)
            )
            
            logger.info(f"Video metadata extracted for {video_asset_id}: {metadata}")
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "current": 20,
                    "total": 100,
                    "status": "Generating proxy video...",
                    "video_asset_id": video_asset_id,
                    "metadata": metadata
                }
            )
            
            # Generate proxy video for editing
            proxy_dir = Path(input_path).parent / "proxies"
            proxy_dir.mkdir(exist_ok=True)
            proxy_path = proxy_dir / f"{Path(input_path).stem}_proxy.mp4"
            
            def progress_callback(progress_data):
                """Callback for transcode progress updates."""
                current = 20 + (progress_data["progress"] * 0.5)  # 20-70% for proxy
                self.update_state(
                    state="PROGRESS",
                    meta={
                        "current": current,
                        "total": 100,
                        "status": f"Creating proxy video... {progress_data['progress']:.1f}%",
                        "video_asset_id": video_asset_id,
                        "transcode_progress": progress_data
                    }
                )
            
            proxy_output = loop.run_until_complete(
                video_processing_service.transcode_video(
                    input_path=input_path,
                    output_path=str(proxy_path),
                    preset="fast",
                    quality="proxy",
                    progress_callback=progress_callback
                )
            )
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "current": 75,
                    "total": 100,
                    "status": "Generating thumbnail...",
                    "video_asset_id": video_asset_id,
                    "proxy_path": proxy_output
                }
            )
            
            # Generate thumbnail
            thumbnail_dir = Path(input_path).parent / "thumbnails"
            thumbnail_dir.mkdir(exist_ok=True)
            thumbnail_path = thumbnail_dir / f"{Path(input_path).stem}_thumb.jpg"
            
            # Get thumbnail at 10% of video duration or 5 seconds, whichever is smaller
            duration = metadata["format"]["duration"]
            thumb_time = min(duration * 0.1, 5.0) if duration > 0 else 1.0
            
            thumbnail_output = loop.run_until_complete(
                video_processing_service.generate_thumbnail(
                    input_path=input_path,
                    output_path=str(thumbnail_path),
                    timestamp=thumb_time,
                    width=320,
                    height=240
                )
            )
            
            # Update progress
            self.update_state(
                state="PROGRESS",
                meta={
                    "current": 85,
                    "total": 100,
                    "status": "Generating waveform...",
                    "video_asset_id": video_asset_id,
                    "thumbnail_path": thumbnail_output
                }
            )
            
            # Generate waveform if video has audio
            waveform_path = None
            if metadata["audio"]:
                waveform_dir = Path(input_path).parent / "waveforms"
                waveform_dir.mkdir(exist_ok=True)
                waveform_file = waveform_dir / f"{Path(input_path).stem}_waveform.png"
                
                try:
                    waveform_path = loop.run_until_complete(
                        video_processing_service.generate_waveform(
                            input_path=input_path,
                            output_path=str(waveform_file),
                            width=1920,
                            height=200
                        )
                    )
                except Exception as e:
                    logger.warning(f"Waveform generation failed for {video_asset_id}: {e}")
                    # Continue without waveform
            
            # Final success state
            result = {
                "status": "completed",
                "video_asset_id": video_asset_id,
                "original_path": input_path,
                "proxy_path": proxy_output,
                "thumbnail_path": thumbnail_output,
                "waveform_path": waveform_path,
                "metadata": metadata,
                "processing_completed_at": datetime.now(timezone.utc).isoformat(),
                "file_size": os.path.getsize(input_path)
            }
            
            logger.info(f"Video processing completed for {video_asset_id}")
            return result
            
        finally:
            loop.close()
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Video processing failed for {video_asset_id}: {error_msg}")
        
        # Update task state to failure
        self.update_state(
            state="FAILURE",
            meta={
                "error": error_msg,
                "video_asset_id": video_asset_id,
                "status": "failed"
            }
        )
        
        # Re-raise the exception to mark task as failed
        raise


@celery.task(bind=True, name="generate_video_previews")
def generate_video_previews(
    self,
    video_asset_id: str,
    input_path: str,
    preview_count: int = 10
) -> Dict:
    """
    Generate preview thumbnails for video scrubbing.
    
    Args:
        video_asset_id: UUID of the video asset
        input_path: Path to the video file
        preview_count: Number of preview thumbnails to generate
        
    Returns:
        Dictionary with preview generation results
    """
    try:
        self.update_state(
            state="PROGRESS",
            meta={
                "current": 0,
                "total": 100,
                "status": "Getting video duration...",
                "video_asset_id": video_asset_id
            }
        )
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Get video metadata for duration
            metadata = loop.run_until_complete(
                video_processing_service.validate_video_file(input_path)
            )
            
            duration = metadata["format"]["duration"]
            if duration <= 0:
                raise ValueError("Invalid video duration")
            
            # Create previews directory
            previews_dir = Path(input_path).parent / "previews"
            previews_dir.mkdir(exist_ok=True)
            
            preview_paths = []
            
            # Generate thumbnails at regular intervals
            for i in range(preview_count):
                timestamp = (duration / preview_count) * i
                preview_file = previews_dir / f"{Path(input_path).stem}_preview_{i:03d}.jpg"
                
                self.update_state(
                    state="PROGRESS",
                    meta={
                        "current": (i / preview_count) * 100,
                        "total": 100,
                        "status": f"Generating preview {i+1}/{preview_count}...",
                        "video_asset_id": video_asset_id
                    }
                )
                
                preview_path = loop.run_until_complete(
                    video_processing_service.generate_thumbnail(
                        input_path=input_path,
                        output_path=str(preview_file),
                        timestamp=timestamp,
                        width=160,
                        height=90
                    )
                )
                
                preview_paths.append({
                    "timestamp": timestamp,
                    "path": preview_path,
                    "index": i
                })
            
            result = {
                "status": "completed",
                "video_asset_id": video_asset_id,
                "preview_count": len(preview_paths),
                "preview_paths": preview_paths,
                "duration": duration,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"Video previews generated for {video_asset_id}: {len(preview_paths)} previews")
            return result
            
        finally:
            loop.close()
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Preview generation failed for {video_asset_id}: {error_msg}")
        
        self.update_state(
            state="FAILURE",
            meta={
                "error": error_msg,
                "video_asset_id": video_asset_id,
                "status": "failed"
            }
        )
        
        raise


@celery.task(bind=True, name="transcode_for_web")
def transcode_for_web(
    self,
    video_asset_id: str,
    input_path: str,
    quality: str = "web"
) -> Dict:
    """
    Transcode video for web delivery.
    
    Args:
        video_asset_id: UUID of the video asset
        input_path: Path to the source video file
        quality: Quality level for web transcoding
        
    Returns:
        Dictionary with transcoding results
    """
    try:
        self.update_state(
            state="PROGRESS",
            meta={
                "current": 0,
                "total": 100,
                "status": "Starting web transcoding...",
                "video_asset_id": video_asset_id
            }
        )
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Create web delivery directory
            web_dir = Path(input_path).parent / "web"
            web_dir.mkdir(exist_ok=True)
            web_path = web_dir / f"{Path(input_path).stem}_web.mp4"
            
            def progress_callback(progress_data):
                """Callback for transcode progress updates."""
                self.update_state(
                    state="PROGRESS",
                    meta={
                        "current": progress_data["progress"],
                        "total": 100,
                        "status": f"Transcoding for web... {progress_data['progress']:.1f}%",
                        "video_asset_id": video_asset_id,
                        "transcode_progress": progress_data
                    }
                )
            
            # Transcode for web delivery
            web_output = loop.run_until_complete(
                video_processing_service.transcode_video(
                    input_path=input_path,
                    output_path=str(web_path),
                    preset="medium",
                    quality=quality,
                    progress_callback=progress_callback
                )
            )
            
            result = {
                "status": "completed",
                "video_asset_id": video_asset_id,
                "original_path": input_path,
                "web_path": web_output,
                "quality": quality,
                "file_size": os.path.getsize(web_output),
                "transcoded_at": datetime.now(timezone.utc).isoformat()
            }
            
            logger.info(f"Web transcoding completed for {video_asset_id}")
            return result
            
        finally:
            loop.close()
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Web transcoding failed for {video_asset_id}: {error_msg}")
        
        self.update_state(
            state="FAILURE",
            meta={
                "error": error_msg,
                "video_asset_id": video_asset_id,
                "status": "failed"
            }
        )
        
        raise