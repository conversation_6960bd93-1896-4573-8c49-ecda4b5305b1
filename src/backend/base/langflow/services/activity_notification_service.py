"""
Activity Notification Service
Handles real-time notifications for client activities across all modules
"""

import asyncio
import logging
from typing import Dict, List, Optional, Set, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from langflow.services.synchronization_service import sync_service, SyncEvent
from langflow.services.client_context_service import ClientContextService

logger = logging.getLogger(__name__)


class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class NotificationType(Enum):
    """Types of notifications"""
    CLIENT_ACTIVITY = "client_activity"
    NEW_COMMUNICATION = "new_communication"
    PROJECT_UPDATE = "project_update"
    DEADLINE_APPROACHING = "deadline_approaching"
    CLIENT_STATUS_CHANGE = "client_status_change"
    TASK_ASSIGNED = "task_assigned"
    MILESTONE_REACHED = "milestone_reached"
    FOLLOW_UP_REMINDER = "follow_up_reminder"
    SYSTEM_ALERT = "system_alert"


@dataclass
class NotificationRule:
    """Defines when and how to send notifications"""
    id: str
    name: str
    description: str
    trigger_type: NotificationType
    conditions: Dict[str, Any]
    priority: NotificationPriority
    recipients: List[str]  # user IDs or roles
    channels: List[str]  # websocket, email, push, etc.
    template: str
    enabled: bool = True
    workspace_id: str = None
    created_by: str = None
    created_at: datetime = None


@dataclass
class Notification:
    """Individual notification instance"""
    id: str
    rule_id: str
    notification_type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    client_id: str
    workspace_id: str
    entity_type: str
    entity_id: str
    recipients: List[str]
    channels: List[str]
    data: Dict[str, Any]
    created_at: datetime
    read_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    actions: List[Dict[str, str]] = None  # Quick actions available


@dataclass
class NotificationPreferences:
    """User notification preferences"""
    user_id: str
    workspace_id: str
    enabled_types: Set[NotificationType]
    enabled_channels: Set[str]
    quiet_hours: Dict[str, str]  # {"start": "22:00", "end": "08:00"}
    priority_threshold: NotificationPriority
    client_specific_rules: Dict[str, Dict[str, Any]]
    updated_at: datetime


class NotificationChannel:
    """Base class for notification delivery channels"""
    
    def __init__(self, channel_name: str):
        self.channel_name = channel_name
    
    async def send_notification(
        self,
        notification: Notification,
        recipient_id: str
    ) -> bool:
        """Send notification through this channel"""
        raise NotImplementedError("Subclasses must implement send_notification")


class WebSocketNotificationChannel(NotificationChannel):
    """WebSocket real-time notification channel"""
    
    def __init__(self):
        super().__init__("websocket")
        self.active_connections: Dict[str, Set] = {}
    
    def register_connection(self, user_id: str, websocket):
        """Register user WebSocket connection"""
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        self.active_connections[user_id].add(websocket)
    
    def unregister_connection(self, user_id: str, websocket):
        """Unregister user WebSocket connection"""
        if user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
    
    async def send_notification(
        self,
        notification: Notification,
        recipient_id: str
    ) -> bool:
        """Send notification via WebSocket"""
        connections = self.active_connections.get(recipient_id, set())
        
        if not connections:
            return False
        
        message = {
            "type": "notification",
            "notification": {
                "id": notification.id,
                "type": notification.notification_type.value,
                "priority": notification.priority.value,
                "title": notification.title,
                "message": notification.message,
                "client_id": notification.client_id,
                "entity_type": notification.entity_type,
                "entity_id": notification.entity_id,
                "data": notification.data,
                "created_at": notification.created_at.isoformat(),
                "actions": notification.actions or []
            }
        }
        
        delivered = False
        dead_connections = set()
        
        for websocket in connections:
            try:
                await websocket.send_text(json.dumps(message))
                delivered = True
            except Exception as e:
                logger.warning(f"Failed to send WebSocket notification: {e}")
                dead_connections.add(websocket)
        
        # Clean up dead connections
        for dead_conn in dead_connections:
            connections.discard(dead_conn)
        
        return delivered


class EmailNotificationChannel(NotificationChannel):
    """Email notification channel"""
    
    def __init__(self):
        super().__init__("email")
    
    async def send_notification(
        self,
        notification: Notification,
        recipient_id: str
    ) -> bool:
        """Send notification via email"""
        # This would integrate with actual email service
        logger.info(f"Sending email notification to {recipient_id}: {notification.title}")
        return True


class ActivityNotificationService:
    """Main service for activity notifications"""
    
    def __init__(self):
        self.notification_rules: Dict[str, List[NotificationRule]] = {}  # workspace_id -> rules
        self.user_preferences: Dict[str, NotificationPreferences] = {}  # user_id -> preferences
        self.notification_history: Dict[str, List[Notification]] = {}  # workspace_id -> notifications
        self.channels: Dict[str, NotificationChannel] = {
            "websocket": WebSocketNotificationChannel(),
            "email": EmailNotificationChannel()
        }
        from langflow.services.database.providers.firebase_crm_service import get_firebase_crm_service
        crm_service = get_firebase_crm_service()
        self.context_service = ClientContextService(crm_service)
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default notification rules"""
        default_rules = [
            NotificationRule(
                id="new_communication",
                name="New Communication",
                description="Notify when new communication is logged",
                trigger_type=NotificationType.NEW_COMMUNICATION,
                conditions={"priority": "medium"},
                priority=NotificationPriority.MEDIUM,
                recipients=["assigned_user", "client_manager"],
                channels=["websocket", "email"],
                template="New {communication_type} from {client_name}: {subject}"
            ),
            NotificationRule(
                id="project_milestone",
                name="Project Milestone",
                description="Notify when project milestone is reached",
                trigger_type=NotificationType.MILESTONE_REACHED,
                conditions={"project_priority": "high"},
                priority=NotificationPriority.HIGH,
                recipients=["project_team", "client_manager"],
                channels=["websocket", "email"],
                template="Milestone '{milestone_name}' reached for client {client_name}"
            ),
            NotificationRule(
                id="deadline_warning",
                name="Deadline Warning",
                description="Notify when deadline is approaching",
                trigger_type=NotificationType.DEADLINE_APPROACHING,
                conditions={"days_before": 3},
                priority=NotificationPriority.HIGH,
                recipients=["assigned_user", "project_manager"],
                channels=["websocket", "email"],
                template="Deadline approaching: {task_name} due in {days_remaining} days"
            ),
            NotificationRule(
                id="client_status_change",
                name="Client Status Change",
                description="Notify when client status changes",
                trigger_type=NotificationType.CLIENT_STATUS_CHANGE,
                conditions={"status_changes": ["lead_to_client", "inactive_to_active"]},
                priority=NotificationPriority.MEDIUM,
                recipients=["client_manager", "sales_team"],
                channels=["websocket"],
                template="Client {client_name} status changed from {old_status} to {new_status}"
            )
        ]
        
        # Add default rules to all workspaces (in real implementation, this would be more sophisticated)
        for workspace_id in ["default"]:
            self.notification_rules[workspace_id] = default_rules
    
    async def subscribe_to_activity_events(self):
        """Subscribe to sync service events for activity monitoring"""
        # This would integrate with the sync service event system
        logger.info("Subscribed to activity events for notifications")
    
    async def process_activity_event(self, event: SyncEvent):
        """Process activity event and generate notifications if needed"""
        try:
            # Find applicable notification rules
            rules = await self._find_applicable_rules(event)
            
            for rule in rules:
                if await self._evaluate_rule_conditions(rule, event):
                    notification = await self._create_notification(rule, event)
                    if notification:
                        await self._send_notification(notification)
        
        except Exception as e:
            logger.error(f"Error processing activity event for notifications: {e}")
    
    async def _find_applicable_rules(self, event: SyncEvent) -> List[NotificationRule]:
        """Find notification rules applicable to the event"""
        workspace_rules = self.notification_rules.get(event.workspace_id, [])
        applicable_rules = []
        
        for rule in workspace_rules:
            if not rule.enabled:
                continue
            
            # Match event type to notification trigger type
            if await self._event_matches_trigger(event, rule.trigger_type):
                applicable_rules.append(rule)
        
        return applicable_rules
    
    async def _event_matches_trigger(
        self,
        event: SyncEvent,
        trigger_type: NotificationType
    ) -> bool:
        """Check if event matches notification trigger type"""
        
        if trigger_type == NotificationType.CLIENT_ACTIVITY:
            return True  # All events are client activities
        
        elif trigger_type == NotificationType.NEW_COMMUNICATION:
            return event.event_type == "communication_added"
        
        elif trigger_type == NotificationType.PROJECT_UPDATE:
            return event.module == "project_management" and "updated" in event.event_type
        
        elif trigger_type == NotificationType.CLIENT_STATUS_CHANGE:
            return event.event_type == "client_updated" and "status" in event.data
        
        elif trigger_type == NotificationType.MILESTONE_REACHED:
            return event.entity_type == "milestone" and "completed" in event.event_type
        
        elif trigger_type == NotificationType.TASK_ASSIGNED:
            return event.entity_type == "task" and event.event_type == "task_assigned"
        
        return False
    
    async def _evaluate_rule_conditions(
        self,
        rule: NotificationRule,
        event: SyncEvent
    ) -> bool:
        """Evaluate if rule conditions are met for the event"""
        
        conditions = rule.conditions
        
        # Check priority condition
        if "priority" in conditions:
            event_priority = event.data.get("priority", "medium")
            if event_priority != conditions["priority"]:
                return False
        
        # Check project priority
        if "project_priority" in conditions:
            project_priority = event.data.get("project_priority", "medium")
            if project_priority != conditions["project_priority"]:
                return False
        
        # Check status changes
        if "status_changes" in conditions:
            old_status = event.data.get("old_status")
            new_status = event.data.get("new_status")
            if old_status and new_status:
                status_change = f"{old_status}_to_{new_status}"
                if status_change not in conditions["status_changes"]:
                    return False
        
        # Check deadline proximity
        if "days_before" in conditions:
            deadline = event.data.get("deadline")
            if deadline:
                try:
                    deadline_date = datetime.fromisoformat(deadline)
                    days_until = (deadline_date - datetime.now()).days
                    if days_until != conditions["days_before"]:
                        return False
                except (ValueError, TypeError):
                    return False
        
        return True
    
    async def _create_notification(
        self,
        rule: NotificationRule,
        event: SyncEvent
    ) -> Optional[Notification]:
        """Create notification from rule and event"""
        
        try:
            # Get client context for template variables
            client_context = await self.context_service.get_client_context(
                client_id=event.client_id,
                workspace_id=event.workspace_id,
                user_id="system"  # System user for notification context
            )
            
            # Prepare template variables
            template_vars = {
                "client_name": client_context.client.get("name", "Unknown Client") if client_context else "Unknown Client",
                "client_id": event.client_id,
                "entity_type": event.entity_type,
                "entity_id": event.entity_id,
                "module": event.module,
                **event.data
            }
            
            # Format notification message
            try:
                title = rule.template.format(**template_vars)
                message = self._create_detailed_message(rule, event, template_vars)
            except KeyError as e:
                logger.warning(f"Missing template variable {e}, using fallback message")
                title = f"Activity notification for {template_vars.get('client_name', 'client')}"
                message = f"New {event.event_type} activity recorded"
            
            # Create notification
            notification = Notification(
                id=f"notif_{int(datetime.now().timestamp())}_{event.client_id}",
                rule_id=rule.id,
                notification_type=rule.trigger_type,
                priority=rule.priority,
                title=title,
                message=message,
                client_id=event.client_id,
                workspace_id=event.workspace_id,
                entity_type=event.entity_type,
                entity_id=event.entity_id,
                recipients=await self._resolve_recipients(rule.recipients, event),
                channels=rule.channels,
                data={
                    "event_type": event.event_type,
                    "module": event.module,
                    "timestamp": event.timestamp.isoformat(),
                    **event.data
                },
                created_at=datetime.now(),
                actions=self._create_notification_actions(rule, event)
            )
            
            return notification
            
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
            return None
    
    def _create_detailed_message(
        self,
        rule: NotificationRule,
        event: SyncEvent,
        template_vars: Dict[str, Any]
    ) -> str:
        """Create detailed notification message"""
        
        base_message = rule.template.format(**template_vars)
        
        # Add context based on notification type
        if rule.trigger_type == NotificationType.NEW_COMMUNICATION:
            details = f"Communication type: {event.data.get('communication_type', 'Unknown')}"
            if event.data.get('urgent'):
                details += " (URGENT)"
            base_message += f"\n{details}"
        
        elif rule.trigger_type == NotificationType.PROJECT_UPDATE:
            details = f"Project: {event.data.get('project_name', 'Unknown')}"
            if event.data.get('status'):
                details += f" | Status: {event.data['status']}"
            base_message += f"\n{details}"
        
        elif rule.trigger_type == NotificationType.DEADLINE_APPROACHING:
            days_remaining = event.data.get('days_remaining', 'Unknown')
            base_message += f"\nAction required within {days_remaining} days"
        
        return base_message
    
    def _create_notification_actions(
        self,
        rule: NotificationRule,
        event: SyncEvent
    ) -> List[Dict[str, str]]:
        """Create quick actions for notification"""
        
        actions = []
        
        if rule.trigger_type == NotificationType.NEW_COMMUNICATION:
            actions.extend([
                {"label": "View Communication", "action": "view_communication", "entity_id": event.entity_id},
                {"label": "Reply", "action": "reply_communication", "entity_id": event.entity_id},
                {"label": "Mark as Read", "action": "mark_read", "entity_id": event.entity_id}
            ])
        
        elif rule.trigger_type == NotificationType.PROJECT_UPDATE:
            actions.extend([
                {"label": "View Project", "action": "view_project", "entity_id": event.entity_id},
                {"label": "Update Status", "action": "update_project_status", "entity_id": event.entity_id}
            ])
        
        elif rule.trigger_type == NotificationType.DEADLINE_APPROACHING:
            actions.extend([
                {"label": "View Task", "action": "view_task", "entity_id": event.entity_id},
                {"label": "Extend Deadline", "action": "extend_deadline", "entity_id": event.entity_id},
                {"label": "Mark Complete", "action": "complete_task", "entity_id": event.entity_id}
            ])
        
        # Always add dismiss action
        actions.append({"label": "Dismiss", "action": "dismiss_notification", "entity_id": event.entity_id})
        
        return actions
    
    async def _resolve_recipients(
        self,
        recipient_specs: List[str],
        event: SyncEvent
    ) -> List[str]:
        """Resolve recipient specifications to actual user IDs"""
        
        recipients = []
        
        for spec in recipient_specs:
            if spec == "assigned_user":
                # Get assigned user from event data
                assigned_user = event.data.get("assigned_user_id")
                if assigned_user:
                    recipients.append(assigned_user)
            
            elif spec == "client_manager":
                # Get client manager from client context
                # This would query the actual client data
                recipients.append("client_manager_user_id")  # Mock
            
            elif spec == "project_team":
                # Get project team members
                # This would query actual project data
                recipients.extend(["team_member_1", "team_member_2"])  # Mock
            
            elif spec == "sales_team":
                # Get sales team members
                recipients.extend(["sales_user_1", "sales_user_2"])  # Mock
            
            elif spec.startswith("user:"):
                # Direct user ID specification
                user_id = spec.replace("user:", "")
                recipients.append(user_id)
            
            elif spec.startswith("role:"):
                # Role-based recipients
                role = spec.replace("role:", "")
                # This would query users by role
                recipients.extend([f"user_with_role_{role}"])  # Mock
        
        return list(set(recipients))  # Remove duplicates
    
    async def _send_notification(self, notification: Notification):
        """Send notification through configured channels"""
        
        # Store notification in history
        workspace_id = notification.workspace_id
        if workspace_id not in self.notification_history:
            self.notification_history[workspace_id] = []
        
        self.notification_history[workspace_id].append(notification)
        
        # Keep only last 1000 notifications per workspace
        self.notification_history[workspace_id] = self.notification_history[workspace_id][-1000:]
        
        # Send through each channel to each recipient
        delivery_results = {}
        
        for recipient_id in notification.recipients:
            # Check user preferences
            prefs = await self._get_user_preferences(recipient_id, workspace_id)
            
            if not await self._should_send_notification(notification, prefs):
                continue
            
            delivery_results[recipient_id] = {}
            
            for channel_name in notification.channels:
                if channel_name not in prefs.enabled_channels:
                    continue
                
                channel = self.channels.get(channel_name)
                if channel:
                    try:
                        success = await channel.send_notification(notification, recipient_id)
                        delivery_results[recipient_id][channel_name] = success
                        
                        if success:
                            logger.info(
                                f"Sent notification {notification.id} to {recipient_id} via {channel_name}"
                            )
                    except Exception as e:
                        logger.error(f"Failed to send notification via {channel_name}: {e}")
                        delivery_results[recipient_id][channel_name] = False
        
        # Update notification delivery status
        if any(any(results.values()) for results in delivery_results.values()):
            notification.delivered_at = datetime.now()
    
    async def _get_user_preferences(
        self,
        user_id: str,
        workspace_id: str
    ) -> NotificationPreferences:
        """Get user notification preferences"""
        
        key = f"{user_id}:{workspace_id}"
        
        if key not in self.user_preferences:
            # Create default preferences
            self.user_preferences[key] = NotificationPreferences(
                user_id=user_id,
                workspace_id=workspace_id,
                enabled_types={
                    NotificationType.NEW_COMMUNICATION,
                    NotificationType.PROJECT_UPDATE,
                    NotificationType.DEADLINE_APPROACHING,
                    NotificationType.CLIENT_STATUS_CHANGE
                },
                enabled_channels={"websocket", "email"},
                quiet_hours={"start": "22:00", "end": "08:00"},
                priority_threshold=NotificationPriority.MEDIUM,
                client_specific_rules={},
                updated_at=datetime.now()
            )
        
        return self.user_preferences[key]
    
    async def _should_send_notification(
        self,
        notification: Notification,
        preferences: NotificationPreferences
    ) -> bool:
        """Check if notification should be sent based on user preferences"""
        
        # Check if notification type is enabled
        if notification.notification_type not in preferences.enabled_types:
            return False
        
        # Check priority threshold
        priority_order = {
            NotificationPriority.LOW: 1,
            NotificationPriority.MEDIUM: 2,
            NotificationPriority.HIGH: 3,
            NotificationPriority.URGENT: 4
        }
        
        if priority_order[notification.priority] < priority_order[preferences.priority_threshold]:
            return False
        
        # Check quiet hours
        now = datetime.now()
        current_time = now.strftime("%H:%M")
        
        quiet_start = preferences.quiet_hours.get("start")
        quiet_end = preferences.quiet_hours.get("end")
        
        if quiet_start and quiet_end:
            if quiet_start <= current_time or current_time <= quiet_end:
                # Only send urgent notifications during quiet hours
                if notification.priority != NotificationPriority.URGENT:
                    return False
        
        # Check client-specific rules
        client_rules = preferences.client_specific_rules.get(notification.client_id, {})
        if client_rules.get("muted", False):
            return False
        
        return True
    
    async def get_notifications(
        self,
        user_id: str,
        workspace_id: str,
        limit: int = 50,
        offset: int = 0,
        unread_only: bool = False
    ) -> List[Notification]:
        """Get notifications for user"""
        
        workspace_notifications = self.notification_history.get(workspace_id, [])
        
        # Filter by recipient
        user_notifications = [
            notif for notif in workspace_notifications
            if user_id in notif.recipients
        ]
        
        # Filter by read status
        if unread_only:
            user_notifications = [
                notif for notif in user_notifications
                if notif.read_at is None
            ]
        
        # Sort by creation time (newest first)
        user_notifications.sort(key=lambda x: x.created_at, reverse=True)
        
        # Apply pagination
        return user_notifications[offset:offset + limit]
    
    async def mark_notification_read(
        self,
        notification_id: str,
        user_id: str,
        workspace_id: str
    ) -> bool:
        """Mark notification as read"""
        
        workspace_notifications = self.notification_history.get(workspace_id, [])
        
        for notification in workspace_notifications:
            if (notification.id == notification_id and 
                user_id in notification.recipients):
                notification.read_at = datetime.now()
                return True
        
        return False
    
    async def update_notification_preferences(
        self,
        user_id: str,
        workspace_id: str,
        preferences: Dict[str, Any]
    ) -> bool:
        """Update user notification preferences"""
        
        key = f"{user_id}:{workspace_id}"
        current_prefs = await self._get_user_preferences(user_id, workspace_id)
        
        # Update fields
        if "enabled_types" in preferences:
            current_prefs.enabled_types = {
                NotificationType(t) for t in preferences["enabled_types"]
            }
        
        if "enabled_channels" in preferences:
            current_prefs.enabled_channels = set(preferences["enabled_channels"])
        
        if "quiet_hours" in preferences:
            current_prefs.quiet_hours = preferences["quiet_hours"]
        
        if "priority_threshold" in preferences:
            current_prefs.priority_threshold = NotificationPriority(preferences["priority_threshold"])
        
        if "client_specific_rules" in preferences:
            current_prefs.client_specific_rules.update(preferences["client_specific_rules"])
        
        current_prefs.updated_at = datetime.now()
        self.user_preferences[key] = current_prefs
        
        return True


# Global instance
notification_service = ActivityNotificationService()